#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟MediaCrawler服务
用于测试舆情监控系统的数据采集功能
"""

from flask import Flask, request, jsonify
import random
import time
from datetime import datetime, timedelta

app = Flask(__name__)

# 模拟小红书数据
MOCK_XHS_DATA = [
    {
        "id": "note_001",
        "platform": "xiaohongshu",
        "type": "post",
        "title": "这款护肤品真的很好用！",
        "content": "用了一个月，皮肤明显变好了，强烈推荐给大家！质量很棒，值得购买。",
        "author": "美妆达人小王",
        "authorId": "user_001",
        "publishTime": (datetime.now() - timedelta(hours=2)).strftime("%Y-%m-%d %H:%M:%S"),
        "url": "https://www.xiaohongshu.com/explore/note_001",
        "likeCount": random.randint(50, 500),
        "commentCount": random.randint(10, 100),
        "shareCount": random.randint(5, 50),
        "viewCount": random.randint(1000, 10000),
        "images": ["https://example.com/image1.jpg"],
        "tags": ["护肤", "美妆", "推荐"],
        "extraData": {"location": "上海", "device": "iPhone"}
    },
    {
        "id": "note_002",
        "platform": "xiaohongshu",
        "type": "post",
        "title": "踩雷了，这个产品不推荐",
        "content": "买了这个产品，用了几天感觉不太好，可能不适合我的肌肤，有点失望。",
        "author": "护肤小白",
        "authorId": "user_002",
        "publishTime": (datetime.now() - timedelta(hours=5)).strftime("%Y-%m-%d %H:%M:%S"),
        "url": "https://www.xiaohongshu.com/explore/note_002",
        "likeCount": random.randint(10, 100),
        "commentCount": random.randint(5, 30),
        "shareCount": random.randint(1, 10),
        "viewCount": random.randint(500, 2000),
        "images": ["https://example.com/image2.jpg"],
        "tags": ["护肤", "踩雷", "不推荐"],
        "extraData": {"location": "北京", "device": "Android"}
    },
    {
        "id": "note_003",
        "platform": "xiaohongshu",
        "type": "post",
        "title": "中性评价，还可以吧",
        "content": "这个产品用起来还行，没有特别惊艳，也没有特别差，就是普通的感觉。",
        "author": "理性消费者",
        "authorId": "user_003",
        "publishTime": (datetime.now() - timedelta(hours=8)).strftime("%Y-%m-%d %H:%M:%S"),
        "url": "https://www.xiaohongshu.com/explore/note_003",
        "likeCount": random.randint(20, 200),
        "commentCount": random.randint(8, 50),
        "shareCount": random.randint(2, 20),
        "viewCount": random.randint(800, 5000),
        "images": ["https://example.com/image3.jpg"],
        "tags": ["护肤", "评测", "中性"],
        "extraData": {"location": "广州", "device": "iPhone"}
    }
]

# 模拟评论数据
MOCK_COMMENTS = [
    {
        "id": "comment_001",
        "platform": "xiaohongshu",
        "type": "comment",
        "content": "我也用过这个，确实不错！",
        "author": "路人甲",
        "authorId": "user_101",
        "publishTime": (datetime.now() - timedelta(hours=1)).strftime("%Y-%m-%d %H:%M:%S"),
        "likeCount": random.randint(1, 20),
        "extraData": {"parent_id": "note_001"}
    },
    {
        "id": "comment_002",
        "platform": "xiaohongshu",
        "type": "comment",
        "content": "同感，我用了也觉得一般般",
        "author": "路人乙",
        "authorId": "user_102",
        "publishTime": (datetime.now() - timedelta(hours=3)).strftime("%Y-%m-%d %H:%M:%S"),
        "likeCount": random.randint(1, 15),
        "extraData": {"parent_id": "note_002"}
    }
]

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return "OK"

@app.route('/api/crawl', methods=['POST'])
def crawl_data():
    """模拟数据爬取接口"""
    try:
        data = request.get_json()
        platform = data.get('platform', 'xiaohongshu')
        keywords = data.get('keywords', [])
        limit = data.get('limit', 10)
        crawl_type = data.get('type', 'search')
        
        print(f"收到爬取请求: platform={platform}, keywords={keywords}, limit={limit}")
        
        # 模拟处理时间
        time.sleep(random.uniform(1, 3))
        
        if platform == 'xiaohongshu':
            # 根据关键词过滤数据
            filtered_data = []
            for item in MOCK_XHS_DATA:
                if not keywords or any(keyword in item['title'] + item['content'] for keyword in keywords):
                    # 随机化一些数据
                    item_copy = item.copy()
                    item_copy['likeCount'] = random.randint(50, 500)
                    item_copy['commentCount'] = random.randint(10, 100)
                    item_copy['viewCount'] = random.randint(1000, 10000)
                    filtered_data.append(item_copy)
            
            # 限制返回数量
            result_data = filtered_data[:limit]
            
            return jsonify({
                "status": "success",
                "message": "数据爬取成功",
                "data": result_data,
                "total": len(result_data),
                "page": 1,
                "pageSize": limit
            })
        else:
            return jsonify({
                "status": "error",
                "message": f"不支持的平台: {platform}",
                "data": [],
                "total": 0
            })
            
    except Exception as e:
        print(f"爬取数据时发生错误: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "data": [],
            "total": 0
        }), 500

@app.route('/api/comments', methods=['POST'])
def get_comments():
    """模拟获取评论接口"""
    try:
        data = request.get_json()
        platform = data.get('platform', 'xiaohongshu')
        content_id = data.get('content_id', '')
        limit = data.get('limit', 10)
        
        print(f"收到评论请求: platform={platform}, content_id={content_id}, limit={limit}")
        
        # 模拟处理时间
        time.sleep(random.uniform(0.5, 2))
        
        # 过滤相关评论
        filtered_comments = [
            comment for comment in MOCK_COMMENTS 
            if comment['extraData'].get('parent_id') == content_id
        ]
        
        result_data = filtered_comments[:limit]
        
        return jsonify({
            "status": "success",
            "message": "评论获取成功",
            "data": result_data,
            "total": len(result_data),
            "page": 1,
            "pageSize": limit
        })
        
    except Exception as e:
        print(f"获取评论时发生错误: {str(e)}")
        return jsonify({
            "status": "error",
            "message": str(e),
            "data": [],
            "total": 0
        }), 500

@app.route('/api/platforms', methods=['GET'])
def get_supported_platforms():
    """获取支持的平台列表"""
    return jsonify({
        "status": "success",
        "data": [
            {"name": "xiaohongshu", "display_name": "小红书", "enabled": True},
            {"name": "douyin", "display_name": "抖音", "enabled": True},
            {"name": "weibo", "display_name": "微博", "enabled": True},
            {"name": "bilibili", "display_name": "B站", "enabled": True}
        ]
    })

@app.route('/', methods=['GET'])
def index():
    """首页"""
    return jsonify({
        "service": "Mock MediaCrawler",
        "version": "1.0.0",
        "description": "模拟MediaCrawler服务，用于测试舆情监控系统",
        "endpoints": [
            "GET /health - 健康检查",
            "POST /api/crawl - 数据爬取",
            "POST /api/comments - 获取评论",
            "GET /api/platforms - 支持的平台"
        ]
    })

if __name__ == '__main__':
    print("启动Mock MediaCrawler服务...")
    print("服务地址: http://localhost:8000")
    print("健康检查: http://localhost:8000/health")
    print("API文档: http://localhost:8000/")
    
    app.run(host='0.0.0.0', port=8000, debug=True)
