# 舆情监控系统数据采集测试脚本

Write-Host "=== 舆情监控系统数据采集测试 ===" -ForegroundColor Green

# 配置
$baseUrl = "http://localhost:8080/api"
$headers = @{
    "Content-Type" = "application/json"
}

# 1. 检查系统健康状态
Write-Host "`n1. 检查系统健康状态..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-WebRequest -Uri "$baseUrl/system/health" -Method GET
    $healthData = $healthResponse.Content | ConvertFrom-Json
    Write-Host "系统状态: $($healthData.status)" -ForegroundColor Green
    Write-Host "数据库状态: $($healthData.database.status)" -ForegroundColor Green
    Write-Host "MediaCrawler状态: $($healthData.mediacrawler.status)" -ForegroundColor $(if($healthData.mediacrawler.status -eq "UP") {"Green"} else {"Red"})
} catch {
    Write-Host "健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 2. 查看当前监控配置
Write-Host "`n2. 查看当前监控配置..." -ForegroundColor Yellow
try {
    $configResponse = Invoke-WebRequest -Uri "$baseUrl/config/monitor" -Method GET
    $configs = $configResponse.Content | ConvertFrom-Json
    Write-Host "找到 $($configs.Count) 个监控配置:" -ForegroundColor Green
    foreach ($config in $configs) {
        Write-Host "  - $($config.name) ($($config.platform)) - 启用: $($config.isEnabled)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "获取监控配置失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 手动采集小红书数据
Write-Host "`n3. 手动采集小红书数据..." -ForegroundColor Yellow
$collectBody = @{
    platform = "xiaohongshu"
    keywords = @("护肤", "美妆", "品牌")
    limit = 10
} | ConvertTo-Json

try {
    $collectResponse = Invoke-WebRequest -Uri "$baseUrl/sentiment/collect" -Method POST -Body $collectBody -Headers $headers
    $collectData = $collectResponse.Content | ConvertFrom-Json
    Write-Host "采集状态: $($collectData.status)" -ForegroundColor Green
    Write-Host "采集平台: $($collectData.platform)" -ForegroundColor Green
    Write-Host "关键词: $($collectData.keywords -join ', ')" -ForegroundColor Green
    Write-Host "限制数量: $($collectData.limit)" -ForegroundColor Green
} catch {
    Write-Host "数据采集失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请确保Mock MediaCrawler服务正在运行 (python scripts/mock_mediacrawler.py)" -ForegroundColor Yellow
}

# 等待数据处理
Write-Host "`n等待数据处理..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# 4. 查看采集到的数据
Write-Host "`n4. 查看采集到的数据..." -ForegroundColor Yellow
try {
    $dataResponse = Invoke-WebRequest -Uri "$baseUrl/sentiment/data?platform=xiaohongshu&size=5" -Method GET
    $dataResult = $dataResponse.Content | ConvertFrom-Json
    
    if ($dataResult.content -and $dataResult.content.Count -gt 0) {
        Write-Host "成功采集到 $($dataResult.content.Count) 条数据:" -ForegroundColor Green
        foreach ($item in $dataResult.content) {
            Write-Host "  - ID: $($item.id)" -ForegroundColor Cyan
            Write-Host "    标题: $($item.title)" -ForegroundColor White
            Write-Host "    情感: $($item.sentiment) (置信度: $($item.sentimentScore))" -ForegroundColor $(
                switch ($item.sentiment) {
                    "positive" { "Green" }
                    "negative" { "Red" }
                    default { "Yellow" }
                }
            )
            Write-Host "    作者: $($item.author)" -ForegroundColor Gray
            Write-Host "    采集时间: $($item.crawlTime)" -ForegroundColor Gray
            Write-Host ""
        }
    } else {
        Write-Host "暂无数据，可能需要等待更长时间或检查MediaCrawler服务" -ForegroundColor Yellow
    }
} catch {
    Write-Host "获取数据失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 查看情感分析统计
Write-Host "`n5. 查看情感分析统计..." -ForegroundColor Yellow
try {
    $statsResponse = Invoke-WebRequest -Uri "$baseUrl/sentiment/stats/sentiment?platform=xiaohongshu" -Method GET
    $statsData = $statsResponse.Content | ConvertFrom-Json
    
    if ($statsData.total -gt 0) {
        Write-Host "情感分析统计 (总计: $($statsData.total)):" -ForegroundColor Green
        foreach ($sentiment in $statsData.counts.PSObject.Properties) {
            $percentage = $statsData.percentages.($sentiment.Name)
            Write-Host "  - $($sentiment.Name): $($sentiment.Value) 条 ($([math]::Round($percentage, 1))%)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "暂无统计数据" -ForegroundColor Yellow
    }
} catch {
    Write-Host "获取统计数据失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 6. 测试情感分析功能
Write-Host "`n6. 测试情感分析功能..." -ForegroundColor Yellow
$testTexts = @(
    "这个产品真的很棒，强烈推荐！",
    "用了很失望，完全不值这个价格",
    "还可以吧，没有特别的感觉"
)

foreach ($text in $testTexts) {
    $analyzeBody = @{
        text = $text
    } | ConvertTo-Json
    
    try {
        $analyzeResponse = Invoke-WebRequest -Uri "$baseUrl/sentiment/analyze" -Method POST -Body $analyzeBody -Headers $headers
        $analyzeData = $analyzeResponse.Content | ConvertFrom-Json
        
        Write-Host "文本: $text" -ForegroundColor White
        Write-Host "情感: $($analyzeData.result.sentiment) (置信度: $([math]::Round($analyzeData.result.confidence, 2)))" -ForegroundColor $(
            switch ($analyzeData.result.sentiment) {
                "positive" { "Green" }
                "negative" { "Red" }
                default { "Yellow" }
            }
        )
        Write-Host ""
    } catch {
        Write-Host "情感分析失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 7. 查看系统统计
Write-Host "`n7. 查看系统统计..." -ForegroundColor Yellow
try {
    $systemStatsResponse = Invoke-WebRequest -Uri "$baseUrl/system/stats" -Method GET
    $systemStats = $systemStatsResponse.Content | ConvertFrom-Json
    
    Write-Host "系统统计信息:" -ForegroundColor Green
    Write-Host "  - 总数据量: $($systemStats.data.total)" -ForegroundColor Cyan
    Write-Host "  - 今日数据: $($systemStats.data.today)" -ForegroundColor Cyan
    Write-Host "  - 监控配置: $($systemStats.monitor_configs.enabled)/$($systemStats.monitor_configs.total) 启用" -ForegroundColor Cyan
    Write-Host "  - 预警规则: $($systemStats.alert_rules.enabled)/$($systemStats.alert_rules.total) 启用" -ForegroundColor Cyan
} catch {
    Write-Host "获取系统统计失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如需启动Mock MediaCrawler服务，请运行: python scripts/mock_mediacrawler.py" -ForegroundColor Yellow
Write-Host "如需查看更多数据，请访问: http://localhost:8080/api/sentiment/data" -ForegroundColor Yellow
