package com.ld.clean.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.base.clean.utils.BasePersonUtil;
import com.ld.clean.context.ApplicationCoreContextManager;
import com.ld.clean.core.dto.company.Company;
import com.ld.clean.dao.pojo.NlpAddressExtractModel;
import com.ld.clean.pojo.bo.caserole.CaseRoleExtractEntity_v1;
import com.ld.clean.dto.company.QccCompanyOut;
import com.ld.clean.model.basecleandatatmp.WjpMrRiskJudgementCase20230316;
import com.ld.clean.model.baseriskdb.CaseAllDimNameandkey;
import com.ld.clean.model.manageriskdb.RiskFyyCasedetail;
import com.ld.clean.repository.baseriskdb.CaseAllDimNameandkeyRepository;
import com.ld.clean.repository.manageriskdb.RiskFyyCasedetailRepository;
import com.ld.clean.repository.tidbsearchsyncrisk.RiskLawsAnnouncementSessionSyncRepository;
import com.ld.clean.utils.CompanyDetailsUtil;
import com.ld.clean.utils.CopyBeanUtil;
import com.ld.clean.utils.DateUtil;
import com.ld.clean.utils.Log;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Condition;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class BaseUtil {

    public static final CaseAllDimNameandkeyRepository CASE_ALL_DIM_NAMEANDKEY_REPOSITORY = ApplicationCoreContextManager.getInstance(CaseAllDimNameandkeyRepository.class);

    public static final RiskFyyCasedetailRepository FYY_CASEDETAIL_REPOSITORY = ApplicationCoreContextManager.getInstance(RiskFyyCasedetailRepository.class);

    private final static Pattern p = Pattern.compile("[\\u4e00-\\u9fa5]");
    // 案号后缀词
    public static String regex = "之[一二三四五六七八九十]$";
    public static Pattern pattern = Pattern.compile(regex);

    public static String getCompanyNameByName(String param) {
        return getString(param);
    }


    static String getString(String param) {
        if (StringUtils.isNotBlank(param) && !param.equals("")) {
            ArrayList<String> strings = new ArrayList<>();
            for (int i = 0; i < param.split(",").length; i++) {
                String s1 = param.split(",")[i];
                Matcher m = BaseUtil.p.matcher(s1);
                if (!m.find()) {
                    s1 = s1.replace("（", "(").replace("）", ")");
                } else {
                    // 其他情况，返回中文括号
                    s1 = s1.replace("(", "（").replace(")", "）");
                }
                strings.add(s1);
            }
            String subjectRelKeywords = strings.stream().map(Objects::toString).collect(Collectors.joining(","));
            //去重
            return Arrays.stream(subjectRelKeywords.split(",")).distinct().collect(Collectors.joining(","));
        } else return param;
    }


    public static String getCompanyNameByNameV2(Map<String, String> companyOrPersonKeyNoMapFinal, String... partKeyNoArrays) {
        List<Company> allCompanies = Lists.newArrayList();
        if (partKeyNoArrays != null && partKeyNoArrays.length > 0) {
            try {
                List<Company> originalKeyNoArrayList = Arrays.stream(partKeyNoArrays).filter(StringUtils::isNotBlank).map(keyNoArray -> JSONArray.parseArray(keyNoArray, Company.class))
                        .flatMap(Collection::stream).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(originalKeyNoArrayList)) {
                    allCompanies.addAll(originalKeyNoArrayList);
                }
            } catch (Exception e) {
                Log.getInst().error(ApplicationCoreContextManager.getAppName(), "getCompanyNameByNameV2,error:{},data:{}", ExceptionUtils.getStackTrace(e), partKeyNoArrays);
            }
        }
        for (Company company : allCompanies) {
            if (companyOrPersonKeyNoMapFinal == null) {
                companyOrPersonKeyNoMapFinal = new HashedMap();
            }
            companyOrPersonKeyNoMapFinal.put(company.getName(), company.getKeyNo());
        }
        if (companyOrPersonKeyNoMapFinal != null) {
            ArrayList<String> strings = new ArrayList<>();
            companyOrPersonKeyNoMapFinal.forEach((k, v) -> {
                Matcher m = p.matcher(k);
                if (!m.find()) {
                    k = k.replace("（", "(").replace("）", ")");
                } else {
                    // 其他情况，返回中文括号
                    k = k.replace("(", "（").replace(")", "）");
                }
                strings.add(k);
                strings.add(v);
            });
            String subjectRelKeywords = strings.stream().map(Objects::toString).collect(Collectors.joining(","));
            //去重
            return Arrays.stream(subjectRelKeywords.split(",")).distinct().collect(Collectors.joining(","));
        } else return null;
    }

    /**
     * 清洗爬虫当事人字段
     *
     * @param originParties 爬虫当事人新
     * @return 清洗结果
     */
    public static String cleanOriginParties(String originParties) {
        List<String> appellor = new ArrayList<>();
        if (StringUtils.isNotEmpty(originParties)) {
            String[] opSplit = originParties.split("[,，]");
            for (String s : opSplit) {
                s = s.replace("(", "（").replace(")", "）").replace("\"", "").replace("[", "").replace("]", "");
                if (s.length() < 2) {
                    continue;
                }
                if (s.matches(".*[某×xX*ｘＸ✘✖ΧχⅹХх].*")) {
                    continue;
                }
                if (tuominNameList.contains(s)) {
                    continue;
                }
                Set<String> filterSet = new LinkedHashSet<>();
                filterSet.add("已提供担保");
                filterSet.add("(一审原告");
                filterSet.add("(一审被告");
                filterSet.add("(原审原告");
                filterSet.add("(原审被告");
                filterSet.add("基本情况");
                filterSet.add("诉请要点");
                filterSet.add("(反诉被告");
                filterSet.add("(反诉原告");
                filterSet.add("答辩要点");
                filterSet.add("对此无异议");
                filterSet.add("本院刑庭");
                filterSet.add("答辩意见");
                filterSet.add("诉讼请求");
                filterSet.add("未到庭应诉");
                filterSet.add("诉称");
                filterSet.add("遂起诉至法院");
                filterSet.add("认为");
                filterSet.add("诉请");
                filterSet.add("辩称");
                filterSet.add("未作答辩");
                if (s.contains("执行案外人") || s.contains("立案庭") || s.contains("审判庭") || s.contains("败诉的") ||
                        s.contains("已预交") || s.contains("刑庭") || s.contains("本院") || s.equals("汽汽车金融有限公司")
                        || s.equals("汽-大众汽车有限公司") || s.equals("三)") || s.equals("基于该宣传广告") || s.equals("上述损失总计") || s.equals("信息")
                        || s.contains("法律") || s.contains("律师") || s.equals("人的)")
                        || s.equals("不同意离婚)")
                        || s.equals("反诉人））")
                        || s.equals("为实现债权）")
                        || s.equals("经追收无果）")
                        || s.equals("表示确无财产可供执行)")
                        || s.equals("接收房屋后）")
                        || s.equals("基本情况）")
                        || s.equals("经催收无果 ）")
                        || s.equals("p&gt  ）")
                        || s.equals("名称）")
                        || s.equals("公司）")
                        || s.equals("下同）)")
                        || s.equals("各方当事人）")
                        || s.equals("健康权）")
                        || s.equals("意见&lt）")
                        || s.equals("故意伤害罪）")
                        || s.equals("出具欠条后）")
                        || s.equals("基本）") || filterSet.contains(s)) {
                    continue;
                }
                try {


                    if (s.contains("（原")) {
                        s = (s.split("（原")[0]);
                    }
                    if (s.contains("（下称")) {
                        s = (s.split("（下称")[0]);
                    }
                    if (s.contains("&times")) {
                        s = (s.replace("&times", ""));
                    }
                    if (s.contains("＆ｌｄｑｕｏ")) {
                        s = (s.replace("＆ｌｄｑｕｏ", ""));
                    }
                    if (s.contains("＆ｔｉｍｅｓ")) {
                        s = (s.replace("＆ｔｉｍｅｓ", ""));
                    }
                    if (s.contains("被执行人")) {
                        s = (s.replace("被执行人", ""));
                    }
                    if (s.contains("执行人")) {
                        s = (s.replace("执行人", ""));
                    }
                    if (s.contains("公民身份号码")) {
                        s = (s.replace("公民身份号码", ""));
                    }
                    if (s.contains("公民")) {
                        s = (s.replace("公民", ""));
                    }
                    if (s.contains("身份证号")) {
                        s = (s.replace("身份证号", ""));
                    }
                    if (s.contains("通过第三人")) {
                        s = (s.replace("通过第三人", ""));
                    }
                } catch (Exception e) {
                    s = "";
                }
                //司|行|队|会|院|局|部|社|厂|厅|所|店|中心|政府|企业|基地|超市|处|矿|室|场|校|城|园|馆|站|组|庭|台|学|吧|庄|户|段|团|村|房|人|家|坊|公寓|库
                if (s.length() > 5) {
                    if (s.contains("·")
                            || s.endsWith("司") || s.endsWith("行") || s.endsWith("队") || s.endsWith("会")
                            || s.endsWith("院") || s.endsWith("局") || s.endsWith("部") || s.endsWith("社")
                            || s.endsWith("厂") || s.endsWith("厅") || s.endsWith("所") || s.endsWith("店")
                            || s.endsWith("中心") || s.endsWith("政府") || s.endsWith("企业") || s.endsWith("基地")
                            || s.endsWith("超市") || s.endsWith("处") || s.endsWith("矿") || s.endsWith("室")
                            || s.endsWith("场") || s.endsWith("校") || s.endsWith("城") || s.endsWith("园")
                            || s.endsWith("馆") || s.endsWith("站") || s.endsWith("组") || s.endsWith("庭")
                            || s.endsWith("台") || s.endsWith("学") || s.endsWith("吧") || s.endsWith("庄")
                            || s.endsWith("户") || s.endsWith("段") || s.endsWith("团") || s.endsWith("村")
                            || s.endsWith("房") || s.endsWith("人") || s.endsWith("家") || s.endsWith("坊")
                            || s.endsWith("公寓") || s.endsWith("库")
                    ) {
                        appellor.add(s);
                    }
                } else if (s.length() > 1) {

                    appellor.add(s);
                }

            }

        }
        return String.join(",", appellor);
    }


    public static final RiskLawsAnnouncementSessionSyncRepository RISK_LAWS_ANNOUNCEMENT_SESSION_SYNC_REPOSITORY = ApplicationCoreContextManager.getInstance(RiskLawsAnnouncementSessionSyncRepository.class);

    /**
     * 通过开庭补充数据
     *
     * @param dateTime    dateTime
     * @param id          id
     * @param caseNameMap caseNameMap
     * @param list        list
     */
    private static Map<String, List<CaseRoleExtractEntity_v1>> cleanSameCaseRole(LocalDateTime dateTime, String id, Map<String, CaseRoleExtractEntity_v1> caseNameMap,
                                                                                 List<CaseAllDimNameandkey> list, CaseRoleExtractEntity_v1 oldEntity, String caseNo) {

        Map<String, List<CaseRoleExtractEntity_v1>> nameAndRoleMap = new HashedMap();

        if (CollectionUtil.isNotEmpty(list)) {
            for (CaseAllDimNameandkey nameandkey : list) {

                // 代表命中规则同案号补充
                int type = 5;
                switch (nameandkey.getSourcetable()) {
                    case "risk_laws_announcement_session_sync":
                        type = 5;
                        break;
                    case "risk_laws_register_sync":
                        type = 50;
                        break;
                    case "risk_laws_announcement_deliver_sync":
                        type = 51;
                        break;
                    case "risk_laws_announcement_court_sync":
                        type = 52;
                        break;
                    case "case_laws_executed_sync":
                        type = 53;
                        break;
                    case "case_laws_bad_credit_executed_sync":
                        type = 54;
                        break;
                    case "risk_end_execution_case_sync":
                        type = 55;
                        break;
                    case "risk_laws_limit_high_consumption_v2_sync":
                        type = 56;
                        break;
                    case "risk_fyy_casedetail":
                        type = 57;
                        break;
                    case "risk_judgement_case":
                        type = 59;
                        break;
                    default:
                        break;
                }
                // 获取原始身份
                List<JSONObject> nameandKeyno = JSON.parseArray(nameandkey.getNameandkeyno(), JSONObject.class);

                Map<Integer, List<JSONObject>> roleName = nameandKeyno.stream().filter(x -> !x.getString("Role").equals("当事人") && !x.getString("Name").equals(oldEntity.getName())).collect(Collectors.groupingBy(x -> x.getInteger("RoleTag")));


                // 原告->原告
                // 被告->被告
                String s1 = oldEntity.getName();
                // 如果名称已经匹配过 不需要下级维度再再次匹配 直接跳过
                boolean processedFlag = false;
                for (List<CaseRoleExtractEntity_v1> value : nameAndRoleMap.values()) {
                    for (CaseRoleExtractEntity_v1 caseRoleExtractEntity_v1 : value) {
                        if (caseRoleExtractEntity_v1.getName().equals(s1) && caseRoleExtractEntity_v1.getType() != null) {
                            processedFlag = true;
                            break;
                        }
                    }
                }
                if (processedFlag) {
                    continue;
                }
                // 案号含有后缀 和所有方向对比
                if (pattern.matcher(caseNo).find()) {
                    nameAndRoleMap.putAll(getStringRole(dateTime, id, caseNameMap, type, nameandKeyno, s1, caseNo));
                } else {
                    switch (oldEntity.getRoleTag()) {
                        case 0:
                            nameAndRoleMap.putAll(getStringRole(dateTime, id, caseNameMap, type, roleName.get(0), s1, caseNo));
                            break;
                        case 1:
                            nameAndRoleMap.putAll(getStringRole(dateTime, id, caseNameMap, type, roleName.get(1), s1, caseNo));
                            break;
                        case 2:
                            nameAndRoleMap.putAll(getStringRole(dateTime, id, caseNameMap, type, roleName.get(2), s1, caseNo));
                            break;
                        case 3:
                            nameAndRoleMap.putAll(getStringRole(dateTime, id, caseNameMap, type, nameandKeyno, s1, caseNo));
                            break;
                        default:
                            break;
                    }
                }
            }
        }
        return nameAndRoleMap;
    }

    /**
     * 通过标题补充和数据
     *
     * @param dateTime    dateTime
     * @param id          id
     * @param caseNameMap caseNameMap
     * @param list        list
     */
    private static Map<String, List<CaseRoleExtractEntity_v1>> cleanSameCaseRoleByTitle(LocalDateTime dateTime, String id, Map<String, CaseRoleExtractEntity_v1> caseNameMap,
                                                                                        Set<String> list, CaseRoleExtractEntity_v1 oldEntity, String caseNo) {
        Map<String, List<CaseRoleExtractEntity_v1>> nameAndRoleMap = new HashedMap();
        if (CollectionUtil.isNotEmpty(list)) {
            for (String name : list) {
                int type = 10;
                String s1 = oldEntity.getName();
                // 如果名称已经匹配过 不需要下级维度再再次匹配 直接跳过
                boolean processedFlag = false;
                for (List<CaseRoleExtractEntity_v1> value : nameAndRoleMap.values()) {
                    for (CaseRoleExtractEntity_v1 caseRoleExtractEntity_v1 : value) {
                        if (caseRoleExtractEntity_v1.getName().equals(s1) && caseRoleExtractEntity_v1.getType() != null) {
                            processedFlag = true;
                            break;
                        }
                    }
                }
                if (processedFlag) {
                    continue;
                }
                JSONObject roleName = new JSONObject();
                roleName.put("Name", name);
                nameAndRoleMap.putAll(getStringRole(dateTime, id, caseNameMap, type, Collections.singletonList(roleName), s1, caseNo));
            }
        }
        return nameAndRoleMap;

    }

    private static Map<String, List<CaseRoleExtractEntity_v1>> getStringRole(LocalDateTime dateTime, String
            id, Map<String, CaseRoleExtractEntity_v1> caseNameMap, int type, List<JSONObject> roleName, String s2, String caseNo) {
        Map<String, List<CaseRoleExtractEntity_v1>> nameAndRoleMap = new HashedMap();
        try {
            if (CollectionUtils.isEmpty(roleName)) {
                return nameAndRoleMap;
            }
            for (JSONObject jsonObject : roleName) {
                // 名称不相同 进行比较 否则跳过
                String s = Util.getCompanyNameByName(jsonObject.getString("Name"));
                String s1 = s2.replace("公司1", "公司").replace("公司2", "公司");
                if (!caseNameMap.containsKey(s)) {
                    // 正则表达式
                    String regex = "[甲乙丙丁123456789ABCDabcd]";
                    s1 = s1.replaceAll(regex, "");
                    if (!s1.matches(".*[某xX*ｘＸ×].*") && !(s1.equals("公司") || s1.equals("某司") || s1.equals("某") || s1.equals("经营部"))) {
                        continue;
                    }
                    String[] splitNames = s1.split("[某xX*ｘＸ×]");

                    boolean iscontain = false;
                    if (splitNames.length == 0) {
                        iscontain = true;
                    }
                    for (String splitName : splitNames) {
                        if (s.length() < 5 && !s1.matches(".*[某xX*ｘＸ×].*")) {
                            iscontain = true;
                            break;
                        }
                        if (!s.contains(splitName)) {
                            iscontain = true;
                            break;
                        }
                        if (s.length() >= 5 && !s.contains("·") && !s.endsWith(splitNames[splitNames.length - 1])) {
                            iscontain = true;
                            break;
                        }
                    }
                    if (!iscontain) {
                        if (s1.endsWith(s)) {
                            continue;
                        }

                        CaseRoleExtractEntity_v1 CaseRoleExtractEntity_v1 = new CaseRoleExtractEntity_v1();
                        CopyBeanUtil.mergeNull(CaseRoleExtractEntity_v1, caseNameMap.get(s2));
                        String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(s, dateTime, id, null, caseNo);
                        CaseRoleExtractEntity_v1.setSupKeyNo(companyKeyNo);
                        CaseRoleExtractEntity_v1.setSupName(s);
                        CaseRoleExtractEntity_v1.setSupShowName(BasePersonUtil.nameEncry(s));

                        CaseRoleExtractEntity_v1.setType(type);
                        List<CaseRoleExtractEntity_v1> roleExtractEntities = new ArrayList<>();
                        if (nameAndRoleMap.containsKey(s)) {
                            roleExtractEntities = nameAndRoleMap.get(s);
                            roleExtractEntities.add(CaseRoleExtractEntity_v1);
                        } else {
                            roleExtractEntities.add(CaseRoleExtractEntity_v1);
                        }
                        nameAndRoleMap.put(s, roleExtractEntities);
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return nameAndRoleMap;
    }

    /**
     * 从其他风险维度补充当事人信息
     *
     * @param anNo        案号
     * @param dateTime    案件时间
     * @param id          主键
     * @param caseNameMap 原始当事人信息
     * @param s           新当事人名称
     * @return map
     */
    private static Map<String, List<CaseRoleExtractEntity_v1>> getRoleByOtherDimension(String anNo, LocalDateTime dateTime, String id, Map<String,
            CaseRoleExtractEntity_v1> caseNameMap, String s, List<CaseAllDimNameandkey> list, String caseNo) throws Exception {
        int type = 2;
        boolean istrue = false;
        int tag = 3;
        if (StringUtils.isNotEmpty(anNo)) {

            // 排序
            list = caseSort(list);
            //存在相同案号数据
            if (CollectionUtils.isNotEmpty(list)) {
                // 判断同案号是否存在主体
                for (CaseAllDimNameandkey nameandkey : list) {
                    // 原告
                    List<JSONObject> ygArray = JSON.parseArray(nameandkey.getYginfo(), JSONObject.class);
                    try {
                        Map<String, JSONObject> ygNames = ygArray.stream().collect(Collectors.toMap(x -> Util.getCompanyNameByName(x.getString("Name")), x -> x));
                        // 被告
                        List<JSONObject> bgArray = JSON.parseArray(nameandkey.getBginfo(), JSONObject.class);
                        Map<String, JSONObject> bgNames = bgArray.stream().collect(Collectors.toMap(x -> Util.getCompanyNameByName(x.getString("Name")), x -> x));
                        // 获取原始身份
                        List<JSONObject> nameandKeyno = JSON.parseArray(nameandkey.getNameandkeyno(), JSONObject.class);

                        Map<String, JSONObject> roleName = nameandKeyno.stream().collect(Collectors.toMap(x -> Util.getCompanyNameByName(x.getString("Name")), x -> x));

                        // 判断原告
                        if (ygNames.containsKey(s)) {
                            tag = 0;
                            if (roleName.containsKey(s) && StringUtils.isNotEmpty(roleName.get(s).getString("Role"))) {
                                tag = roleName.get(s).getInteger("RoleTag");
                            }
                            istrue = true;
                            break;
                        } else if (bgNames.containsKey(s)) {
                            // 判断被告
                            tag = 1;
                            if (roleName.containsKey(s) && StringUtils.isNotEmpty(roleName.get(s).getString("Role"))) {
                                tag = roleName.get(s).getInteger("RoleTag");
                            }
                            istrue = true;
                            break;
                        } else if (roleName.containsKey(s)) {
                            tag = 3;
                            if (roleName.containsKey(s) && StringUtils.isNotEmpty(roleName.get(s).getString("Role"))) {
                                tag = roleName.get(s).getInteger("RoleTag");
                            }
                            istrue = true;
                            break;
                        }
                    } catch (Exception ignored) {

                    }

                }
            }
        }
        Map<String, List<CaseRoleExtractEntity_v1>> nameAndRoleMap = new HashedMap();

        if (!istrue) {
            //判断公司主体身份
            nameAndRoleMap = getRoleString(caseNameMap, s, dateTime, id, 4, 3, caseNo);

        } else {
            nameAndRoleMap = getRoleString(caseNameMap, s, dateTime, id, type, tag, caseNo);
        }
        return nameAndRoleMap;
    }

    private static Map<String, List<CaseRoleExtractEntity_v1>> cleanAppellorContains
            (Map<String, CaseRoleExtractEntity_v1> caseNameMap, String s, String role, int tag, LocalDateTime
                    datatime, String id, String caseNo) throws Exception {
        Map<String, List<CaseRoleExtractEntity_v1>> nameAndRoleMap = new HashedMap();
        int i = 0;
        String name = "";
        boolean notAdd = false;
        for (String s1 : caseNameMap.keySet()) {
            int tagCase = 3;
            try {
                tagCase = caseNameMap.get(s1).getRoleTag();

            } catch (Exception ignored) {

            }
            if (tag == tagCase || tag == 3) {
                // 1 完全相等 无需处理
                if (caseNameMap.containsKey(s)) {
                    break;
                }
                String tempName = s1;
                String regex = "[甲乙丙丁123456789ABCDabcd]";
                s1 = s1.replaceAll(regex, "");
                if (!s1.matches(".*[某xX*ｘＸ×].*") && !(s1.equals("公司") || s1.equals("某司") || s1.equals("某") || s1.equals("经营部"))) {

                    continue;
                }
                String[] splitNames = s1.split("[某xX*ｘＸ]");
                if (splitNames.length == 0) {
                    continue;
                }

                boolean iscontain = false;
                for (String splitName : splitNames) {
                    if (s.length() < 5 && !s1.matches(".*[某xX*ｘＸ].*")) {
                        iscontain = true;
                        break;
                    }
                    if (!s.contains(splitName)) {
                        iscontain = true;
                        break;
                    }
                    if (s.length() > 5 && !s.endsWith(splitNames[splitNames.length - 1])) {
                        iscontain = true;
                        break;
                    }
                }

                //@王经鹏
                //接口主体比解析主体短，且后缀相同的 就不新增进去了

                if (!iscontain) {
                    name = tempName;
                    if (s1.endsWith(s)) {
                        continue;
                    }
                    i++;
                    CaseRoleExtractEntity_v1 CaseRoleExtractEntity_v1 = new CaseRoleExtractEntity_v1();
                    CopyBeanUtil.mergeNull(CaseRoleExtractEntity_v1, caseNameMap.get(name));

                    String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(s, datatime, id, null, caseNo);
                    CaseRoleExtractEntity_v1.setSupKeyNo(companyKeyNo);
                    CaseRoleExtractEntity_v1.setSupName(s);
                    CaseRoleExtractEntity_v1.setSupShowName(BasePersonUtil.nameEncry(s));

                    CaseRoleExtractEntity_v1.setType(1);
                    List<CaseRoleExtractEntity_v1> roleExtractEntities = new ArrayList<>();
                    if (nameAndRoleMap.containsKey(s)) {
                        roleExtractEntities = nameAndRoleMap.get(s);
                        roleExtractEntities.add(CaseRoleExtractEntity_v1);
                    } else {
                        roleExtractEntities.add(CaseRoleExtractEntity_v1);
                    }
                    nameAndRoleMap.put(s, roleExtractEntities);
                }
            }
        }
        if (i == 0) {
            String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(s, datatime, id, null, caseNo);
            CaseRoleExtractEntity_v1 caseRoleExtractEntity_v1 = CaseRoleExtractEntity_v1.builder().Role(role).RoleTag(tag).KeyNo(companyKeyNo).Name(s).build();
            caseRoleExtractEntity_v1.setType(1);
            caseRoleExtractEntity_v1.setSupKeyNo(companyKeyNo);
            caseRoleExtractEntity_v1.setSupName(s);
            caseRoleExtractEntity_v1.setSupShowName(BasePersonUtil.nameEncry(s));

            nameAndRoleMap.put(s, Arrays.asList(caseRoleExtractEntity_v1));
        }
        return nameAndRoleMap;
    }

    private static Map<String, List<CaseRoleExtractEntity_v1>> getRoleString
            (Map<String, CaseRoleExtractEntity_v1> caseNameMap, String s, LocalDateTime dataTime, String id,
             int type, int tag, String caseNo) throws Exception {
        String name;
        Map<String, List<CaseRoleExtractEntity_v1>> nameAndRoleMap = new HashedMap();
        for (String s1 : caseNameMap.keySet()) {
            // 1 完全相等 无需处理
            if (caseNameMap.containsKey(s)) {
                break;
            }
            String temps1 = s1;
            int oldTag = caseNameMap.get(s1).getRoleTag();
            // 正则表达式
            // 角色类型必须要匹配
            String regex = "[甲乙丙丁123456789ABCDabcd]";
            s1 = s1.replaceAll(regex, "");
            if (!s1.matches(".*[某xX*ｘＸ×].*") && !(s1.equals("公司") || s1.equals("某司") || s1.equals("某") || s1.equals("经营部"))) {

                continue;
            }
            if (oldTag != tag && tag != 3 && oldTag != 3) {
                continue;
            }


            String[] splitNames = s1.split("[某xX*ｘＸ×]");

            boolean iscontain = false;
            if (splitNames.length == 0) {
                iscontain = true;
            }
            for (String splitName : splitNames) {
                if (s.length() < 5 && !s1.matches(".*[某xX*ｘＸ×].*")) {
                    iscontain = true;
                    break;
                }

                if (!s.contains(splitName)) {
                    iscontain = true;
                    break;
                }
                if (s.length() >= 5 && !s.endsWith(splitNames[splitNames.length - 1])) {
                    iscontain = true;
                    break;
                }
            }

            if (!iscontain) {
                name = temps1;

                //接口主体比解析主体短，且后缀相同的 就不新增进去了
                if (s1.endsWith(s)) {
                    continue;
                }
                CaseRoleExtractEntity_v1 CaseRoleExtractEntity_v1 = new CaseRoleExtractEntity_v1();
                CopyBeanUtil.mergeNull(CaseRoleExtractEntity_v1, caseNameMap.get(name));

                String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(s, dataTime, id, null, caseNo);
                CaseRoleExtractEntity_v1.setSupKeyNo(companyKeyNo);
                CaseRoleExtractEntity_v1.setSupName(s);
                CaseRoleExtractEntity_v1.setSupShowName(BasePersonUtil.nameEncry(s));

                CaseRoleExtractEntity_v1.setType(type);
                List<CaseRoleExtractEntity_v1> roleExtractEntities = new ArrayList<>();
                if (nameAndRoleMap.containsKey(s)) {
                    roleExtractEntities = nameAndRoleMap.get(s);
                    roleExtractEntities.add(CaseRoleExtractEntity_v1);
                } else {
                    roleExtractEntities.add(CaseRoleExtractEntity_v1);
                }
                nameAndRoleMap.put(s, roleExtractEntities);
            }

        }

        return nameAndRoleMap;
    }

    /**
     * 按照表名称进行排序
     * 开庭>立案>送达>法院>被执行>失信>终本>限高
     * risk_laws_announcement_session_sync >risk_laws_register_sync >risk_laws_announcement_deliver_sync>risk_laws_announcement_court_sync>case_laws_executed_sync>case_laws_bad_credit_executed_sync>risk_end_execution_case_sync>risk_laws_limit_high_consumption_v2_sync
     * 优先级
     */
    public static Map<String, Integer> typeOrder = new HashMap<>();

    static {
        // 定义类型顺序
        typeOrder.put("risk_judgement_case", 0);
        typeOrder.put("risk_laws_announcement_session_sync", 1);
        typeOrder.put("risk_laws_register_sync", 2);
        typeOrder.put("risk_laws_announcement_deliver_sync", 3);
        typeOrder.put("risk_laws_announcement_court_sync", 4);
        typeOrder.put("case_laws_executed_sync", 6);
        typeOrder.put("case_laws_bad_credit_executed_sync", 7);
        typeOrder.put("risk_end_execution_case_sync", 8);
        typeOrder.put("risk_laws_limit_high_consumption_v2_sync", 5);
        typeOrder.put("risk_fyy_casedetail", 9);


    }

    public static List<CaseAllDimNameandkey> caseSort(List<CaseAllDimNameandkey> list) {
        // 自定义 Comparator 来实现排序规则
        list.sort(new Comparator<CaseAllDimNameandkey>() {
            @Override
            public int compare(CaseAllDimNameandkey o1, CaseAllDimNameandkey o2) {
                return Integer.compare(typeOrder.get(o1.getSourcetable()), typeOrder.get(o2.getSourcetable()));
            }
        });
        return list;
    }

    // 特殊脱敏名称
    public static List<String> tuominNameList = Arrays.asList("甲公司", "公司甲", "乙公司", "公司乙", "丙公司", "公司丙", "丙公司", "某某公司1", "某某公司2", "某某公司3", "某某公司4",
            "某某公司5", "某某公司6", "某某公司7", "某某公司8", "某某公司9", "某某公司10", "某司甲", "某司乙", "某司丙", "某司丁", "某1", "某2", "某3", "某4", "某5", "某6", "某7", "某8",
            "某9", "某10", "甲经营部", "A公司", "B公司", "C公司", "D公司", "a公司", "b公司", "c公司", "d公司", "公司A", "公司B", "公司C", "公司D", "公司a", "公司b", "公司c", "公司d", "某公司", "某银行");
    // 法人名称
    public static List<String> operEnum = Arrays.asList("法定代表人", "法人代表", "法定代表", "法人", "企业法人", "负责人", "主要负责人", "单位负责人");


    public static String cleanAppellorRole(String aiResult, String partys, String trialStr, String
            courtCode, String anNo, String originParties, LocalDateTime dateTime, String id, String nlpResult,
                                           WjpMrRiskJudgementCase20230316 wjpMrCaseLawsBankruptcy20230718,
                                           Map<String, String> companyOrPersonKeyNoMap, Set<String> removeKeyNoSet) throws Exception {
        if (StringUtils.isEmpty(aiResult)) {
            return "[]";
        }
        // 爬虫当事人清洗
        String appllor = BaseUtil.cleanOriginParties(originParties);

        // 获取法人信息
        Map<String, String> operMap = new HashedMap();
        // 地址信息
        Map<String, NlpAddressExtractModel> addressMap = new HashedMap();
        // 工商数据信息
        Map<String, QccCompanyOut> qccCompanyOuts = new HashedMap();

        if (StringUtils.isNotEmpty(partys)) {
            partys = ExtractCompanyInfoV14.convertWithLineFeed(partys).replace("\n", "");
            partys = Util.getCompanyNameByName(partys);
        }


        //和正文身份映射
        if (StringUtils.isNotEmpty(aiResult)) {
            aiResult = Util.getCompanyNameByName(aiResult);

        }
        // 接口主体和解析主体去重
        List<CaseRoleExtractEntity_v1> jsonArray = JSONObject.parseArray(aiResult, CaseRoleExtractEntity_v1.class);
        jsonArray.forEach(x -> {
            if (x.getRoleTag() == null) {
                x.setRoleTag(3);
            }
        });
        Map<String, CaseRoleExtractEntity_v1> caseNameMap = jsonArray.stream().collect(Collectors.toMap(CaseRoleExtractEntity_v1::getName, t -> t, (o, n) -> n));
        //段落当事人提取
        Map<String, String> respRoleMap = RoleUtils.getRoleInfo("", partys, trialStr, courtCode, anNo, appllor, caseNameMap.keySet());

        List<CaseAllDimNameandkey> list = new ArrayList<>();
        // 使用同案号判断
        if (StringUtils.isNotEmpty(anNo)) {
            Condition condition = new Condition(CaseAllDimNameandkey.class);
            condition.createCriteria().andEqualTo("cleanCaseNo", CaseNoCleanUtils.cleanCaseNo(anNo))
                    .andNotIn("dataStatus", Arrays.asList(2, 4, 5)).andNotEqualTo("sourcetable", "risk_judgement_case");
            list = CASE_ALL_DIM_NAMEANDKEY_REPOSITORY.selectByCondition(condition);

            Set<String> names = new HashSet<>();
            list.forEach(x -> {
                List<JSONObject> jsonObjectList = JSON.parseArray(x.getNameandkeyno(), JSONObject.class);
                if (CollectionUtils.isEmpty(jsonObjectList)) {
                    return;
                }
                if (x.getSourcetable().equals("risk_fyy_casedetail") || x.getSourcetable().equals("risk_judgement_case")) {
                    if (StringUtils.isNotEmpty(x.getNameandkeyno())) {
                        jsonObjectList.forEach(y -> {
                            y.put("Name", y.getString("P"));
                            Integer rt = ConfigStaticDataFile.CASE_ROLE_NAME_MAP_TAG.get(y.getString("R"));
                            y.put("RoleTag", rt == null ? 3 : rt);
                            y.put("Role", y.getString("R"));
                            y.put("KeyNo", y.getString("N"));
                            y.put("Org", y.getString("O"));
                            y.remove("P");
                            y.remove("N");
                            y.remove("O");
                            y.remove("R");
                            names.add(y.getString("P"));
                        });
                        x.setNameandkeyno(JSON.toJSONString(jsonObjectList));
                    }
                } else {
                    try {
                        jsonObjectList.forEach(y -> {

                            String name = y.getString("P");
                            String name2 = y.getString("Name");
                            name = StringUtils.isNotEmpty(name) ? name : name2;
                            names.add(name);

                        });
                    } catch (Exception e) {

                    }

                }
            });
        }

        Map<String, List<CaseRoleExtractEntity_v1>> nameAndRoleMap = new HashedMap();
        if (respRoleMap.size() > 0) {
            //需要保留的当事人
            for (String s : respRoleMap.keySet()) {
                if (caseNameMap.containsKey(s)) {
                    continue;
                }
                String role = respRoleMap.get(s).split("_")[0];
                //爬虫当事人类型
                int tag = RoleUtils.roleTagMap.get(role);
                // 正文和标题没有出现
                if (respRoleMap.get(s).equals("当事人")) {
                    // 从其他维度补充当事人信息 v2.0
                    nameAndRoleMap.putAll(getRoleByOtherDimension(anNo, dateTime, id, caseNameMap, s, list, anNo));
                    continue;
                }
                //2.个人去重
                if (s.length() < 5) {
                    // 分段判断包含关系
                    nameAndRoleMap.putAll(cleanAppellorContains(caseNameMap, s, role, tag, dateTime, id, anNo));
                } else {
                    //公司去重
                    if (s.endsWith("司") || s.endsWith("行") || s.endsWith("队") || s.endsWith("会")
                            || s.endsWith("院") || s.endsWith("局") || s.endsWith("部") || s.endsWith("社")
                            || s.endsWith("厂") || s.endsWith("厅") || s.endsWith("所") || s.endsWith("店")
                            || s.endsWith("中心") || s.endsWith("政府") || s.endsWith("企业") || s.endsWith("基地")
                            || s.endsWith("超市") || s.endsWith("处") || s.endsWith("矿") || s.endsWith("室")
                            || s.endsWith("场") || s.endsWith("校") || s.endsWith("城") || s.endsWith("园")
                            || s.endsWith("馆") || s.endsWith("站") || s.endsWith("组") || s.endsWith("庭")
                            || s.endsWith("台") || s.endsWith("学") || s.endsWith("吧") || s.endsWith("庄")
                            || s.endsWith("户") || s.endsWith("段") || s.endsWith("团") || s.endsWith("村")
                            || s.endsWith("房")) {
                        nameAndRoleMap.putAll(cleanAppellorContains(caseNameMap, s, role, tag, dateTime, id, anNo));
                    }
                }
            }
            Set<String> allNameKyno = new HashSet<>();
            // 获取所有待补充当事人的keyno
            for (String s : respRoleMap.keySet()) {
                String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(s, dateTime, id, null, anNo);
                if (StringUtils.isNotEmpty(companyKeyNo) && !caseNameMap.containsKey(s)) {
                    allNameKyno.add(companyKeyNo);
                }
            }
            Map<Integer, Set<String>> roleNamesMap = new HashedMap();
            roleNamesMap.put(3, allNameKyno);
            cleanOnlyCompanyFuc(partys, nlpResult, wjpMrCaseLawsBankruptcy20230718, operMap, addressMap, caseNameMap, nameAndRoleMap, qccCompanyOuts, roleNamesMap, id, dateTime, anNo);
        }
        if (dateTime == null) {
            dateTime = LocalDateTime.now();
        }
        if (dateTime.getYear() > 2015) {


            LocalDateTime finalDateTime = dateTime;
            List<CaseAllDimNameandkey> list2 = getCaseAllDimNameandkeys(finalDateTime, list);
            caseNameMap.values().forEach(entity -> {
                if (StringUtils.isEmpty(entity.getSupName()) && (entity.getName().matches(".*[某xX*ｘＸ×].*") || tuominNameList.contains(entity.getName()))) {
                    nameAndRoleMap.putAll(cleanSameCaseRole(finalDateTime, id, caseNameMap, list2, entity, anNo));
                }
            });
            if (CollectionUtils.isNotEmpty(list2)) {
                Map<Integer, Set<String>> roleNamesMap = new HashedMap();
                for (CaseAllDimNameandkey nameandkey : list2) {

                    List<JSONObject> nameandKeyno = JSON.parseArray(nameandkey.getNameandkeyno(), JSONObject.class);

                    for (JSONObject object : nameandKeyno) {
                        if (StringUtils.isNotEmpty(object.getString("KeyNo")) && !object.getString("KeyNo").startsWith("p") && !caseNameMap.containsKey(object.getString("Name"))) {
                            Integer roleTag = object.getIntValue("RoleTag");
                            if (roleNamesMap.containsKey(roleTag)) {
                                Set<String> allNameKyno = roleNamesMap.get(roleTag);
                                allNameKyno.add(object.getString("KeyNo"));
                                roleNamesMap.put(roleTag, allNameKyno);
                            } else {
                                Set<String> allNameKyno = new HashSet<>();
                                allNameKyno.add(object.getString("KeyNo"));
                                roleNamesMap.put(roleTag, allNameKyno);
                            }
                        }
                    }
                }
                cleanOnlyCompanyFuc(partys, nlpResult, wjpMrCaseLawsBankruptcy20230718, operMap, addressMap, caseNameMap, nameAndRoleMap, qccCompanyOuts, roleNamesMap, id, dateTime, anNo);
            }
        }

        List<String> names = new ArrayList<>();




        for (CaseRoleExtractEntity_v1 entity : caseNameMap.values()) {

            if(Arrays.asList("315cab3d81514b99b2478538d437288e0",
                    "6913d4006c5c40c58aa9175cf6293e610",
                    "f8d40ed5e47649baad503f34ff32dc340","03a76fa072fb4d72a8f4b566b35499d90","044c226fdb1941e7abc84e3946dbbe7e0").contains(id)){
                String newName="";
                if(entity.getName().equals("贵州B公司")){
                    newName="贵州滴藏酒业(集团)有限公司";

                }
                if(entity.getName().equals("A公司")){
                    newName="众诺（上海）化工科技有限公司";

                }
                if(entity.getName().equals("惠州市A有限公司")){
                    newName="惠州市德勤志商务信息咨询有限公司";

                }
                if(entity.getName().equals("上海B有限公司")){
                    newName="上海销氪信息科技有限公司";

                }
                if(entity.getName().equals("北京安某某有限公司")){
                    newName="上海销氪信息科技有限公司";

                }
                if(entity.getName().equals("青岛某某某（有限合伙）")){
                    newName="青岛昙曜投资合伙企业（有限合伙）";

                }
                if(entity.getName().equals("天某某限责任公司")){
                    newName="天九共享智慧企业服务股份有限公司";

                }
                if(entity.getName().equals("北京某某（有限合伙）")){
                    newName=" 北京鼎逸企业管理咨询中心（有限合伙）";
                }


                String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(newName, dateTime, id, null, anNo);
                entity.setSupKeyNo(companyKeyNo);
                entity.setSupName(newName);
                entity.setSupShowName(BasePersonUtil.nameEncry(newName));
                entity.setType(1);
                continue;

            }




            if (StringUtils.isEmpty(entity.getSupName()) && (entity.getName().matches(".*[某xX*ｘＸ×].*") || tuominNameList.contains(entity.getName()))) {
                return aiResult;
            }
            // 防止出现一个公司替换多个当事人的情况 如果存在就不补充
            if (StringUtils.isNotEmpty(entity.getSupName())) {
                if (names.contains(entity.getSupName())) {
                    return aiResult;
                } else {
                    names.add(entity.getSupName());
                }
            }
            if (StringUtils.isNotEmpty(entity.getSupName()) && StringUtils.isEmpty(entity.getSupKeyNo())) {
                entity.setSupKeyNo(companyOrPersonKeyNoMap.get(entity.getSupName()));
            }
            if (StringUtils.isNotEmpty(entity.getSupName()) && StringUtils.isNotEmpty(entity.getName()) && entity.getSupName().equals(entity.getName())) {
                entity.setSupName(null);
                entity.setSupKeyNo(null);
                entity.setSupShowName(null);
            }
            if (StringUtils.isNotEmpty(entity.getSupName()) && StringUtils.isEmpty(entity.getSupKeyNo())) {
                entity.setSupKeyNo(Util.getCompanyNameByName(companyOrPersonKeyNoMap.get(entity.getSupName())));
            }
            if (StringUtils.isEmpty(entity.getKeyNo())) {
                entity.setKeyNo(Util.getCompanyNameByName(companyOrPersonKeyNoMap.get(entity.getName())));
            }
            if (StringUtils.isNotEmpty(entity.getKeyNo()) && StringUtils.isNotEmpty(entity.getSupName())) {
                removeKeyNoSet.add(entity.getKeyNo());
                entity.setKeyNo("");
            }
        }
        return caseNameMap.values().toString();
    }

    private static List<CaseAllDimNameandkey> getCaseAllDimNameandkeys(LocalDateTime finalDateTime, List<CaseAllDimNameandkey> finalList) {


        List<CaseAllDimNameandkey> kaiTingInfo = new ArrayList<>(finalList.stream()
                .filter(x -> Arrays.asList("risk_laws_announcement_session_sync")
                        .contains(x.getSourcetable()) && StringUtils.isNotEmpty(x.getNameandkeyno()) && !x.getNameandkeyno().contains("SupNameAndKeyNo")
                        && JSON.parseArray(x.getNameandkeyno(), JSONObject.class).size() > 1)
                .collect(Collectors.groupingBy(x -> x.getNameandkeyno().length()))
                .entrySet()
                .stream()
                .max(Map.Entry.comparingByKey())
                .map(Map.Entry::getValue)
                .orElse(Collections.emptyList()));
        // 长度相同查询sessiondate 进行判断
        List<CaseAllDimNameandkey> list2 = new ArrayList<>();
        if (kaiTingInfo.size() >= 1) {
            kaiTingInfo = kaiTingInfo.stream().filter(x -> x.getRiskDate() <= DateUtil.getTimestampOfDateTime(finalDateTime))
                    .sorted(Comparator.comparing(CaseAllDimNameandkey::getRiskDate).reversed()
                            .thenComparing(CaseAllDimNameandkey::getWdUpdateDate))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(kaiTingInfo)) {
                list2.add(kaiTingInfo.get(0));
            }
        }

        //立案
        List<CaseAllDimNameandkey> liAnInfo = finalList.stream()
                .filter(x -> Objects.equals("risk_laws_register_sync", x.getSourcetable())
                        && StringUtils.isNotEmpty(x.getNameandkeyno()) && !x.getNameandkeyno().contains("SupNameAndKeyNo") && JSON.parseArray(x.getNameandkeyno(), JSONObject.class).size() > 1
                        && x.getRiskDate() != null && x.getRiskDate() <= DateUtil.getTimestampOfDateTime(finalDateTime)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(liAnInfo)) {
            list2.addAll(liAnInfo);
        }

        // 送达  // 法院
        List<CaseAllDimNameandkey> songdaInfo = finalList.stream()
                .filter(x -> Arrays.asList("risk_laws_announcement_deliver_sync", "risk_laws_announcement_court_sync", "risk_laws_register_sync")
                        .contains(x.getSourcetable()) && StringUtils.isNotEmpty(x.getNameandkeyno()) && !x.getNameandkeyno().contains("SupNameAndKeyNo") && JSON.parseArray(x.getNameandkeyno(), JSONObject.class).size() > 1)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(songdaInfo)) {
            list2.addAll(songdaInfo);
        }

        // 执行 失信 终本 限高
        List<CaseAllDimNameandkey> fayuanInfo = finalList.stream()
                .filter(x -> Arrays.asList("case_laws_executed_sync", "case_laws_bad_credit_executed_sync",
                        "risk_end_execution_case_sync", "risk_laws_limit_high_consumption_v2_sync").contains(x.getSourcetable())
                        && StringUtils.isNotEmpty(x.getNameandkeyno()) && !x.getNameandkeyno().contains("SupNameAndKeyNo")).collect(Collectors.toList());
        // 重新组装nameandkeyno 增加roletag字段
        fayuanInfo.forEach(x -> {
            List<JSONObject> ygList = addRoleTag(x.getYginfo(), 0);
            List<JSONObject> bgList = addRoleTag(x.getBginfo(), 1);
            List<JSONObject> nameAndKeyList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(ygList)) {
                nameAndKeyList.addAll(ygList);
            }
            if (CollectionUtils.isNotEmpty(bgList)) {
                nameAndKeyList.addAll(bgList);
            }
            if (CollectionUtils.isNotEmpty(nameAndKeyList)) {
                x.setNameandkeyno(nameAndKeyList.toString());
            }
        });
        if (CollectionUtils.isNotEmpty(fayuanInfo)) {
            // 合并计算执行类数据 mergeCaseAllDimList(fayuanInfo)
            list2.addAll(mergeCaseAllDimList(fayuanInfo));
        }
        //法研院
        List<CaseAllDimNameandkey> fyyInfo = finalList.stream()
                .filter(x -> Objects.equals("risk_fyy_casedetail", x.getSourcetable())
                        && StringUtils.isNotEmpty(x.getNameandkeyno()) && JSON.parseArray(x.getNameandkeyno(), JSONObject.class).size() > 1
                        && x.getRiskDate() != null && x.getRiskDate() <= DateUtil.getTimestampOfDateTime(finalDateTime)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(fyyInfo)) {
            list2.addAll(fyyInfo);
        }
        List<CaseAllDimNameandkey> cpwsInfo = finalList.stream()
                .filter(x -> (Objects.equals("risk_judgement_case", x.getSourcetable()))
                        && StringUtils.isNotEmpty(x.getNameandkeyno()) && JSON.parseArray(x.getNameandkeyno(), JSONObject.class).size() > 1
                ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cpwsInfo)) {
            list2.addAll(cpwsInfo);
        }
        list2.forEach(x -> {
            List<JSONObject> nameandKeyno = JSON.parseArray(x.getNameandkeyno(), JSONObject.class);
            List<JSONObject> cleanArray = new ArrayList<>();
            boolean isRoleTag3 = false;
            if (CollectionUtils.isNotEmpty(nameandKeyno)) {
                for (JSONObject object : nameandKeyno) {
                    String name = object.getString("Name");
                    String Role = object.getString("Role");

                    String cleanName = BaseUtil.cleanOriginParties(name);
                    if (StringUtils.isNotEmpty(cleanName)) {
                        cleanArray.add(object);
                    }
                    if (StringUtils.isEmpty(Role) || "当事人".equals(Role)) {
                        isRoleTag3 = true;
                    }
                }
                if (CollectionUtils.isNotEmpty(cleanArray) && !isRoleTag3) {
                    x.setNameandkeyno(cleanArray.toString());
                } else {
                    x.setNameandkeyno(null);
                }
            }
        });

        list2 = list2.stream().filter(x -> StringUtils.isNotEmpty(x.getNameandkeyno())).collect(Collectors.toList());

        return caseSort(list2);
    }

    private static void cleanOnlyCompanyFuc(String partys, String nlpResult, WjpMrRiskJudgementCase20230316
            wjpMrCaseLawsBankruptcy20230718, Map<String, String> operMap,
                                            Map<String, NlpAddressExtractModel> addressMap, Map<String, CaseRoleExtractEntity_v1> caseNameMap,
                                            Map<String, List<CaseRoleExtractEntity_v1>> nameAndRoleMap, Map<String, QccCompanyOut> qccCompanyOuts,
                                            Map<Integer, Set<String>> allNameKyno, String id, LocalDateTime dateTime, String caseNo) {
        Map<String, List<CaseRoleExtractEntity_v1>> oldNameMoreOne = new HashedMap();
        nameAndRoleMap.forEach((k, v) -> {
            for (CaseRoleExtractEntity_v1 entity : v) {
                if (oldNameMoreOne.containsKey(entity.getName())) {
                    List<CaseRoleExtractEntity_v1> entities = oldNameMoreOne.get(entity.getName());
                    entities.add(entity);
                    oldNameMoreOne.put(entity.getName(), entities);

                } else {
                    List<CaseRoleExtractEntity_v1> entities = new ArrayList<>();
                    entities.add(entity);
                    oldNameMoreOne.put(entity.getName(), entities);
                }
            }
        });

        if (qccCompanyOuts.isEmpty()) {
            // 查询企业法人和地址信息 k= 企业名称
            qccCompanyOuts = getQccCompanyOuts(oldNameMoreOne, allNameKyno);
        }
        // 查询裁判文书法人
        getOperMap(nlpResult, operMap);
        // 查询裁判文书地址
        getCompanyAddressAndOper(partys, caseNameMap.keySet(), addressMap, id);
        // k =新名称 v =对应的老名称
        Map<String, QccCompanyOut> finalQccCompanyOuts = qccCompanyOuts;
        Map<String, QccCompanyOut> finalQccCompanyOuts1 = qccCompanyOuts;
        oldNameMoreOne.forEach((k, v) -> {
            v = v.stream().distinct().collect(Collectors.toList());
            if (v.size() == 1) {
                CaseRoleExtractEntity_v1 extractEntity = v.get(0);
                String name = extractEntity.getName();
                caseNameMap.put(name, extractEntity);
            } else {

                // N:1的情况 获取法人信息 进行法人或地址对比
                //对比法人和地址 姓相同，且在这条数据里唯一，则进行替换。
                // 匹配标识 k=name v=100 /110  第一位 代表命中次数 第二位1代表命中 第三位0 代表未命中
                int i_oper = 0;
                int i_addr = 0;
                Map<String, CaseRoleExtractEntity_v1> conformEntity_oper = new HashedMap();
                Map<String, CaseRoleExtractEntity_v1> conformEntity_addr = new HashedMap();

                for (CaseRoleExtractEntity_v1 entity : v) {
                    String oldSur = "";
                    String qccSur = "";
                    // 先判断法人
                    if (StringUtils.isNotEmpty(operMap.get(entity.getName()))) {
                        oldSur = operMap.get(entity.getName()).substring(0, 1);
                    }

                    if (finalQccCompanyOuts1.get(entity.getSupKeyNo()) != null && finalQccCompanyOuts1.get(entity.getSupKeyNo()).getOper() != null && StringUtils.isNotEmpty(finalQccCompanyOuts1.get(entity.getSupKeyNo()).getOper().getName())) {
                        qccSur = finalQccCompanyOuts1.get(entity.getSupKeyNo()).getOper().getName().substring(0, 1);
                    }

                    if (StringUtils.isNotEmpty(oldSur) && StringUtils.isNotEmpty(qccSur) && qccSur.equals(oldSur)) {
                        // 法人相同
                        i_oper++;
                        conformEntity_oper.put(entity.getSupName(), entity);
                    }
                    NlpAddressExtractModel qccAddrss = null;
                    NlpAddressExtractModel oldAddress = addressMap.get(entity.getName());
                    if (finalQccCompanyOuts1.get(entity.getSupKeyNo()) != null) {
                        qccAddrss = RoleUtils.getResponseAreas(finalQccCompanyOuts1.get(entity.getSupKeyNo()).getAddress(), id);
                    }
                    // 地址相同
//                    if (oldAddress != null && qccAddrss != null && oldAddress.getProvince() != null && oldAddress.getProvince().equals(qccAddrss.getProvince())) {
                    if (compareAddress(oldAddress, qccAddrss)) {
                        i_addr++;
                        conformEntity_addr.put(entity.getSupName(), entity);
                    }
                }
                if (i_oper == 1) {
                    CaseRoleExtractEntity_v1 extractEntity_v1 = conformEntity_oper.values().stream().findFirst().get();
                    caseNameMap.remove(extractEntity_v1.getName());
                    extractEntity_v1.setType(7);
                    caseNameMap.put(extractEntity_v1.getName(), extractEntity_v1);
                } else if (i_addr == 1) {
                    CaseRoleExtractEntity_v1 extractEntity_v1 = conformEntity_addr.values().stream().findFirst().get();
                    extractEntity_v1.setType(8);
                    caseNameMap.remove(extractEntity_v1.getName());
                    caseNameMap.put(extractEntity_v1.getName(), extractEntity_v1);
                } else if (i_oper > 1 && i_addr > 1) {
                    Map<String, CaseRoleExtractEntity_v1> intersection = conformEntity_addr.entrySet().stream()
                            .filter(entry -> conformEntity_oper.containsKey(entry.getKey()) && conformEntity_oper.get(entry.getKey()).equals(entry.getValue()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
                    if (intersection.size() == 1) {
                        CaseRoleExtractEntity_v1 entity = intersection.values().stream().findFirst().get();
                        entity.setType(9);
                        caseNameMap.remove(entity.getName());
                        caseNameMap.put(entity.getName(), entity);
                    }
                }
            }
        });
        Set<String> keynos = caseNameMap.values().stream().filter(x -> StringUtils.isNotEmpty(x.getSupKeyNo())).map(CaseRoleExtractEntity_v1::getSupKeyNo).collect(Collectors.toSet());
        Set<String> oldKeynos = caseNameMap.values().stream().filter(x -> StringUtils.isNotEmpty(x.getKeyNo())).map(CaseRoleExtractEntity_v1::getKeyNo).collect(Collectors.toSet());
        keynos.addAll(oldKeynos);
        if (!finalQccCompanyOuts.isEmpty()) {
            finalQccCompanyOuts.values().removeIf(x -> keynos.contains(x.getKeyNo()));
            Map<String, CaseRoleExtractEntity_v1> caseNameMapTemp = new HashMap<>(caseNameMap);

            for (CaseRoleExtractEntity_v1 v : caseNameMapTemp.values()) {
                // 剩余未补充当事人 并且存在法人和地址关系 直接判断
                if (StringUtils.isEmpty(v.getSupName()) && (v.getName().matches(".*[某xX*ｘＸ×].*") || tuominNameList.contains(v.getName()))) {
                    if (operMap.containsKey(v.getName()) || addressMap.containsKey(v.getName())) {
                        int i_oper = 0;
                        int i_addr = 0;
                        String oldname = null;
                        Map<String, QccCompanyOut> conformEntity_oper = new HashedMap();
                        Map<String, QccCompanyOut> conformEntity_addr = new HashedMap();
                        for (QccCompanyOut company : finalQccCompanyOuts.values()) {
                            String oldSur = "";
                            String qccSur = "";
                            // 名称方向不同
                            // 身份不同 或者身份不是3 不进行反补
                            if (!(isCompanyName(v.getName(), 1) == isCompanyName(company.getName(), 0)
                                    && ((allNameKyno.containsKey(v.getRoleTag()) && allNameKyno.get(v.getRoleTag()).contains(company.getKeyNo())) || v.getRole().equals("当事人")))) {
                                continue;
                            }
                            // 先判断法人
                            if (StringUtils.isNotEmpty(operMap.get(v.getName()))) {
                                oldSur = operMap.get(v.getName()).substring(0, 1);
                            }
                            if (company.getOper() != null && StringUtils.isNotEmpty(company.getOper().getName())) {
                                qccSur = company.getOper().getName().substring(0, 1);
                            }
                            if (StringUtils.isNotEmpty(oldSur) && StringUtils.isNotEmpty(qccSur) && qccSur.equals(oldSur)) {
                                // 法人相同
                                i_oper++;
                                conformEntity_oper.put(company.getName(), company);
                                oldname = v.getName();
                            }
                            NlpAddressExtractModel oldAddress = addressMap.get(v.getName());
                            NlpAddressExtractModel qccAddrss = RoleUtils.getResponseAreas(company.getAddress(), id);
                            // 地址相同
//                            if (oldAddress != null && oldAddress.getProvince() != null && oldAddress.getProvince().equals(qccAddrss.getProvince())) {
                            if (compareAddress(oldAddress, qccAddrss)) {
                                i_addr++;
                                conformEntity_addr.put(company.getName(), company);
                                oldname = v.getName();
                            }
                        }
                        if (i_oper == 1) {
                            CaseRoleExtractEntity_v1 entity = caseNameMap.get(oldname);
                            QccCompanyOut company = conformEntity_oper.values().stream().findFirst().get();
                            caseNameMap.remove(oldname);
                            String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(company.getName(), dateTime, id, null, caseNo);
                            entity.setSupKeyNo(companyKeyNo);
                            entity.setSupName(company.getName());
                            entity.setSupShowName(BasePersonUtil.nameEncry(company.getName()));
                            entity.setType(7);
                            caseNameMap.put(oldname, entity);
                        } else if (i_addr == 1) {
                            CaseRoleExtractEntity_v1 entity = caseNameMap.get(oldname);
                            caseNameMap.remove(oldname);
                            QccCompanyOut company = conformEntity_addr.values().stream().findFirst().get();
                            String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(company.getName(), dateTime, id, null, caseNo);

                            entity.setSupKeyNo(companyKeyNo);
                            entity.setSupName(company.getName());
                            entity.setSupShowName(BasePersonUtil.nameEncry(company.getName()));
                            entity.setType(8);
                            caseNameMap.put(oldname, entity);
                        } else if (i_oper > 1 && i_addr > 1) {
                            conformEntity_addr.keySet().retainAll(conformEntity_oper.keySet());
                            if (conformEntity_addr.size() == 1) {
                                CaseRoleExtractEntity_v1 entity = caseNameMap.get(oldname);
                                caseNameMap.remove(oldname);
                                QccCompanyOut company = conformEntity_addr.values().stream().findFirst().get();
                                String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(company.getName(), dateTime, id, null, caseNo);

                                entity.setSupKeyNo(companyKeyNo);
                                entity.setSupName(company.getName());
                                entity.setSupShowName(BasePersonUtil.nameEncry(company.getName()));
                                entity.setType(9);
                                caseNameMap.put(oldname, entity);
                            }

                        }
                    }
                }
            }
        }
        wjpMrCaseLawsBankruptcy20230718.setKeynoArray(JSON.toJSONString(operMap));
        wjpMrCaseLawsBankruptcy20230718.setCaseroleArray(JSON.toJSONString(addressMap));
        wjpMrCaseLawsBankruptcy20230718.setDefdKeynoArray(JSON.toJSONString(qccCompanyOuts));

        // 疑似重复当事人 张三 张三男 李四 李四等
        Set<String> containNames = new HashSet<>();
        for (String key1 : caseNameMap.keySet()) {
            if (key1.length() <= 5 && (key1.endsWith("男") || key1.endsWith("女") || key1.endsWith("等") || key1.endsWith("等人"))) {
                for (String key2 : caseNameMap.keySet()) {
                    if (key2.length() <= 5) {
                        if (key1.contains(key2) && !key1.equals(key2)) {
                            containNames.add(key1);
                            break;
                        }
                    }
                }
            }
        }
        for (String name : containNames) {
            caseNameMap.remove(name);
        }

    }

    /**
     * 查询工商法人信息
     *
     * @param oldNameMoreOne oldNameMoreOne
     * @return Map
     */
    private static Map<String, QccCompanyOut> getQccCompanyOuts
    (Map<String, List<CaseRoleExtractEntity_v1>> oldNameMoreOne, Map<Integer, Set<String>> allNameKyno) {
        Map<String, QccCompanyOut> qccCompanyOutMap = new HashedMap();

        Set<String> companyKeynos = new HashSet<>();
        oldNameMoreOne.forEach((k, v) -> {
            if (v.size() > 1) {
                Set<String> keynos = v.stream().map(CaseRoleExtractEntity_v1::getSupKeyNo).collect(Collectors.toSet());
                companyKeynos.addAll(keynos);
            }
        });
        if (!allNameKyno.isEmpty()) {
            for (Set<String> value : allNameKyno.values()) {
                companyKeynos.addAll(value);
            }
        }
        if (CollectionUtils.isNotEmpty(companyKeynos)) {
            List<QccCompanyOut> qccCompanyOuts = CompanyDetailsUtil.getCompanyList(new ArrayList<>(companyKeynos), true, Arrays.asList("_id", "Oper", "Name", "Address"));
            for (QccCompanyOut companyOut : qccCompanyOuts) {
                qccCompanyOutMap.put(companyOut.getKeyNo(), companyOut);
            }
        }
        return qccCompanyOutMap;
    }

    /**
     * 解析裁判文书文本法人信息
     *
     * @param nlpResult nlp解析
     * @param operMap   法人
     */
    private static void getOperMap(String nlpResult, Map<String, String> operMap) {
        if (operMap.isEmpty() && StringUtils.isNotEmpty(nlpResult) && !nlpResult.equals("[]") && !nlpResult.equals("null")) {
            List<JSONObject> jsonObjects = JSON.parseArray(nlpResult, JSONObject.class);
            try {
                for (JSONObject object : jsonObjects) {
                    if (object.containsKey("rel_obj")) {
                        JSONArray rel_obj = object.getJSONArray("rel_obj");
                        for (Object o : rel_obj) {
                            JSONObject jsonObject = (JSONObject) o;
                            String relation = jsonObject.getString("relation");
                            if (operEnum.contains(relation)) {
                                operMap.put(object.getString("name"), jsonObject.getString("name"));
                            }
                        }
                    }
                }
            } catch (Exception e) {
            }

        }
    }

    public static Map<String, NlpAddressExtractModel> getCompanyAddressAndOper(String
                                                                                       party, Set<String> names, Map<String, NlpAddressExtractModel> addressExtractModelMap, String id) {

        String contentForMatch = party
                .replace("(", "（").replace(")", "）").replace(":", "：").replace("。住所地：", "，住所地：");


        List<String> segmentList = Arrays.stream(contentForMatch.split("[\n。 ]"))
                .map(e -> e.replace(" ", "").replace("　", ""))
                .map(StringUtils::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        names = names.stream()
                .sorted(Comparator.comparing(String::length).reversed())
                .collect(Collectors.toCollection(LinkedHashSet::new));

        List<String> isCleanText = new ArrayList<>();
        // 文书提取到的涉案人员提取信息对象映射集合
        if (addressExtractModelMap.isEmpty()) {
//            addressExtractModelMap = new HashMap<>();

            for (String segment : segmentList) {
                for (String comName : names) {
                    if (segment.contains(comName) && !isCleanText.contains(segment)) {
                        //提取住所
                        String a = "";
                        for (String s : segment.split("[,，]")) {
                            for (String name : ExtractCompanyInfoV14.ADREES_NAMES) {
                                if (s.startsWith(name)) {
                                    a = s.replace(name, "");
                                    break;
                                }
                            }
                        }
                        if (StringUtils.isNotEmpty(a)) {
                            isCleanText.add(segment);
                            addressExtractModelMap.put(comName, RoleUtils.getResponseAreas(a, id));
                        }

                    }
                }
            }
        }
        return addressExtractModelMap;

    }

    /**
     * 判断公司和自然人
     *
     * @param s    目标名称
     * @param type 0 未脱敏，1脱敏
     * @return boolean
     */
    public static boolean isCompanyName(String s, int type) {
        boolean bo = false;
        if (s.length() > 5 && type == 0) {
            if (s.endsWith("司") || s.endsWith("行") || s.endsWith("队") || s.endsWith("会")
                    || s.endsWith("院") || s.endsWith("局") || s.endsWith("部") || s.endsWith("社")
                    || s.endsWith("厂") || s.endsWith("厅") || s.endsWith("所") || s.endsWith("店")
                    || s.endsWith("中心") || s.endsWith("政府") || s.endsWith("企业") || s.endsWith("基地")
                    || s.endsWith("超市") || s.endsWith("处") || s.endsWith("矿") || s.endsWith("室")
                    || s.endsWith("场") || s.endsWith("校") || s.endsWith("城") || s.endsWith("园")
                    || s.endsWith("馆") || s.endsWith("站") || s.endsWith("组") || s.endsWith("庭")
                    || s.endsWith("台") || s.endsWith("学") || s.endsWith("吧") || s.endsWith("庄")
                    || s.endsWith("户") || s.endsWith("段") || s.endsWith("团") || s.endsWith("村")
                    || s.endsWith("房") || s.endsWith("人") || s.endsWith("家") || s.endsWith("坊")
                    || s.endsWith("公寓") || s.endsWith("库")) {
                bo = true;
            }
        }
        if (s.length() == 3 && type == 1) {
            if (tuominNameList.contains(s)) {
                bo = true;
            }
        }
        if (s.length() >= 4 && type == 1) {
            if ((s.contains("司") || s.contains("行") || s.contains("队") || s.contains("会")
                    || s.contains("院") || s.contains("局") || s.contains("部") || s.contains("社")
                    || s.contains("厂") || s.contains("厅") || s.contains("所") || s.contains("店")
                    || s.contains("中心") || s.contains("政府") || s.contains("企业") || s.contains("基地")
                    || s.contains("超市") || s.contains("处") || s.contains("矿") || s.contains("室")
                    || s.contains("场") || s.contains("校") || s.contains("城") || s.contains("园")
                    || s.contains("馆") || s.contains("站") || s.contains("组") || s.contains("庭")
                    || s.contains("台") || s.contains("学") || s.contains("吧") || s.contains("庄")
                    || s.contains("户") || s.contains("段") || s.contains("团") || s.contains("村")
                    || s.contains("房") || s.contains("人") || s.contains("家") || s.contains("坊")
                    || s.contains("公寓") || s.contains("库")) && !s.contains("·")) {
                bo = true;
            }
        }

        if (s.length() <= 2) {
            bo = false;
        }

        return bo;
    }


    /**
     * 在原nameandkeyno 中增加roletag
     *
     * @param keynoArray k
     * @param roleTag    r
     * @return list
     */
    public static List<JSONObject> addRoleTag(String keynoArray, int roleTag) {
        if (StringUtils.isNotEmpty(keynoArray)) {
            List<JSONObject> ygList = JSON.parseArray(keynoArray, JSONObject.class);
            if (CollectionUtils.isNotEmpty(ygList)) {
                ygList.forEach(y -> {
                    y.put("RoleTag", roleTag);
                    if (roleTag == 0) {
                        y.put("Role", "申请人");
                    } else if (roleTag == 1) {
                        y.put("Role", "被执行人");
                    }
                });
            }
            return ygList;
        }
        return new ArrayList<>();
    }

    /**
     * 合并 执行类案件的当事人信息， 相同案号相同维度 执行人合并成一个数组 只保留一个当事人
     * Arrays.asList("case_laws_executed_sync", "case_laws_bad_credit_executed_sync", "risk_end_execution_case_sync", "risk_laws_limit_high_consumption_v2_sync")
     *
     * @param allDimNameandkeys 所有维度数据
     * @return 合并后唯一的维度数据
     */
    public static List<CaseAllDimNameandkey> mergeCaseAllDimList(List<CaseAllDimNameandkey> allDimNameandkeys) {

        HashedMap zxMap = new HashedMap();
        HashedMap sxMap = new HashedMap();
        HashedMap zbMap = new HashedMap();
        HashedMap xgMap = new HashedMap();
        CaseAllDimNameandkey zxCase = new CaseAllDimNameandkey();
        CaseAllDimNameandkey sxCase = new CaseAllDimNameandkey();
        CaseAllDimNameandkey zbCase = new CaseAllDimNameandkey();
        CaseAllDimNameandkey xgCase = new CaseAllDimNameandkey();

        if (CollectionUtils.isNotEmpty(allDimNameandkeys)) {
            for (CaseAllDimNameandkey nameandkey : allDimNameandkeys) {
                if ("case_laws_executed_sync".equals(nameandkey.getSourcetable())) {
                    zxCase = nameandkey;
                    List<JSONObject> nameandKeyno = JSON.parseArray(nameandkey.getNameandkeyno(), JSONObject.class);
                    for (JSONObject object : nameandKeyno) {
                        zxMap.put(object.getString("Name"), object);
                    }

                } else if ("case_laws_bad_credit_executed_sync".equals(nameandkey.getSourcetable())) {
                    sxCase = nameandkey;
                    List<JSONObject> nameandKeyno = JSON.parseArray(nameandkey.getNameandkeyno(), JSONObject.class);
                    for (JSONObject object : nameandKeyno) {
                        sxMap.put(object.getString("Name"), object);
                    }
                } else if ("risk_end_execution_case_sync".equals(nameandkey.getSourcetable())) {
                    zbCase = nameandkey;
                    List<JSONObject> nameandKeyno = JSON.parseArray(nameandkey.getNameandkeyno(), JSONObject.class);
                    for (JSONObject object : nameandKeyno) {
                        zbMap.put(object.getString("Name"), object);
                    }
                } else if ("risk_laws_limit_high_consumption_v2_sync".equals(nameandkey.getSourcetable())) {
                    xgCase = nameandkey;
                    List<JSONObject> nameandKeyno = JSON.parseArray(nameandkey.getNameandkeyno(), JSONObject.class);
                    for (JSONObject object : nameandKeyno) {
                        xgMap.put(object.getString("Name"), object);
                    }
                }
            }
            // 重新组合NameAndKey
            List<CaseAllDimNameandkey> caseAllDimNameandkeyList = new ArrayList<>();
            //执行
            if (!zxMap.isEmpty()) {
                zxCase.setNameandkeyno(zxMap.values().toString());
                caseAllDimNameandkeyList.add(zxCase);
            }
            //失信
            if (!sxMap.isEmpty()) {
                sxCase.setNameandkeyno(sxMap.values().toString());
                caseAllDimNameandkeyList.add(sxCase);
            }
            //终本
            if (!zbMap.isEmpty()) {
                zbCase.setNameandkeyno(zbMap.values().toString());
                caseAllDimNameandkeyList.add(zbCase);
            }
            //限高
            if (!xgMap.isEmpty()) {
                xgCase.setNameandkeyno(xgMap.values().toString());
                caseAllDimNameandkeyList.add(xgCase);
            }
            return caseAllDimNameandkeyList;
        }
        return allDimNameandkeys;
    }

    // 地址对比判断
    public static boolean compareAddress(NlpAddressExtractModel oldAddress, NlpAddressExtractModel qccAddrss) {
        boolean compareFlag = false;
        if (oldAddress != null && qccAddrss != null) {
            // 深度判断 取最小深度匹配
            int oldDeep = getAddressDeepNum(oldAddress);
            int qccDeep = getAddressDeepNum(qccAddrss);
            int deepNum = Math.min(oldDeep, qccDeep);
            // 最小深度地址要全部符合
            switch (deepNum) {
                case 3:
                    if (isEqual(oldAddress.getCounty(), qccAddrss.getCounty()) &&
                            isEqual(oldAddress.getCity(), qccAddrss.getCity()) &&
                            isEqual(oldAddress.getProvince(), qccAddrss.getProvince())) {
                        compareFlag = true;
                    }
                    break;
                case 2:
                    if (isEqual(oldAddress.getCounty(), qccAddrss.getCounty()) &&
                            isEqual(oldAddress.getCity(), qccAddrss.getCity())) {
                        compareFlag = true;
                    }
                    break;
                case 1:
                    if (isEqual(oldAddress.getProvince(), qccAddrss.getProvince())) {
                        compareFlag = true;
                    }
                    break;
                default:
                    break;
            }
        }
        return compareFlag;
    }

    // 获取地址深度
    public static int getAddressDeepNum(NlpAddressExtractModel addressExtractModel) {
        int deepNum = 0;
        if (addressExtractModel != null) {
            if (StringUtils.isNotEmpty(addressExtractModel.getProvince())) {
                deepNum = 1;
            }
            if (StringUtils.isNotEmpty(addressExtractModel.getCity())) {
                deepNum = 2;
            }
            if (StringUtils.isNotEmpty(addressExtractModel.getProvince())) {
                deepNum = 3;
            }
        }
        return deepNum;
    }

    private static boolean isEqual(String str1, String str2) {
        if (StringUtils.isEmpty(str1) || StringUtils.isEmpty(str2)) {
            return true;
        }
        return str1.equals(str2);
    }

    /**
     * [QDCT-485]法研院数据用于裁判文书标题数据诉讼身份的纠正
     * 案号中存在 ‘初终再执’
     * 法研院当事人中出现诉讼身份'原告%' or ‘被告%’
     */
    public static List<JSONObject> addCaseRoleName(List<JSONObject> titleList, String caseNo, int shieldFlag) {

        if (CollectionUtils.isNotEmpty(titleList) && shieldFlag == 1 && StringUtils.isNotEmpty(caseNo)) {

            List<CaseRoleExtractEntity_v1> jsonArray = JSONObject.parseArray(titleList.toString(), CaseRoleExtractEntity_v1.class);
            List<CaseRoleExtractEntity_v1> entityBack = JSONObject.parseArray(titleList.toString(), CaseRoleExtractEntity_v1.class);

            long count = jsonArray.stream().filter(x -> x.getRole().equals("当事人")).count();

            if (count > 0) {
                Condition condition = new Condition(RiskFyyCasedetail.class);
                // 查询法研院数据
                condition.createCriteria().andEqualTo("caseNoShow", caseNo)
                        .andNotIn("dataStatus", Arrays.asList(2, 4, 5));
                List<RiskFyyCasedetail> fyyList = FYY_CASEDETAIL_REPOSITORY.selectByCondition(condition);
                if (CollectionUtils.isNotEmpty(fyyList)) {
                    if (fyyList.size() > 10) {
                        System.out.println(caseNo + "--" + fyyList.size());
                    }
                    //问题数据过滤
                    fyyList = fyyList.stream().filter(x -> {
                        if (StringUtils.isEmpty(x.getCaseroleArray()) || x.getCaseroleArray().equals("[]")) {
                            return false;
                        }
                        if (x.getCaseNo().contains("初") && x.getCaseroleArray().contains("上诉人")) {
                            return false;
                        }
                        JSONArray roleNames = JSON.parseArray(x.getCaseroleArray());
                        for (Object roleName : roleNames) {
                            JSONObject r = (JSONObject) roleName;
                            String rn = r.getString("R");
                            if (caseNo.matches(".*[终再执].*") && rn.matches("^原告.*|^被告.*")) {
                                return false;
                            }
                            if (Arrays.asList("其他", "未知").contains(rn)) {
                                return false;
                            }
                        }
                        return true;

                    }).collect(Collectors.toList());
                    // 主体替换逻辑
                    if (CollectionUtils.isNotEmpty(fyyList)) {
                        for (RiskFyyCasedetail fyyItem : fyyList) {
                            jsonArray = entityBack;
                            String nameAndKey = fyyItem.getCaseroleArray();
                            List<JSONObject> jsonObjectList = JSON.parseArray(nameAndKey, JSONObject.class);
                            // 法研院 名称和身份关系MAP
                            Map<String, String> fyyNameRole = new HashedMap();
                            //法研院 Keyno和身份关系MAP
                            Map<String, String> fyyKeyNoRole = new HashedMap();

                            for (JSONObject fyyRole : jsonObjectList) {
                                String role = fyyRole.getString("R");
                                if (!"当事人".equals(role)) {
                                    fyyNameRole.put(Util.getCompanyNameByName(fyyRole.getString("P")), role);
                                    if (StringUtils.isNotEmpty(fyyRole.getString("N"))) {
                                        fyyKeyNoRole.put(Util.getCompanyNameByName(fyyRole.getString("N")), role);
                                    }
                                }
                            }
                            //裁判文书
                            for (CaseRoleExtractEntity_v1 entityV1 : jsonArray) {
                                if ("当事人".equals(entityV1.getRole())) {
                                    String name = Util.getCompanyNameByName(entityV1.getName());
                                    String supName = Util.getCompanyNameByName(entityV1.getSupName());
                                    String keyNo = Util.getCompanyNameByName(entityV1.getKeyNo());
                                    String supKeyNo = Util.getCompanyNameByName(entityV1.getSupKeyNo());

                                    if (fyyNameRole.containsKey(name) || fyyNameRole.containsKey(supName) || fyyKeyNoRole.containsKey(keyNo) || fyyKeyNoRole.containsKey(supKeyNo)) {
                                        String roleName = fyyNameRole.containsKey(name) ? fyyNameRole.get(name) : fyyNameRole.get(supName);
                                        String roleName2 = fyyKeyNoRole.containsKey(keyNo) ? fyyKeyNoRole.get(keyNo) : fyyKeyNoRole.get(supKeyNo);
                                        roleName = StringUtils.isNotEmpty(roleName) ? roleName : roleName2;
                                        entityV1.setRole(roleName);
                                        entityV1.setRoleTag(ConfigStaticDataFile.CASE_ROLE_NAME_MAP_TAG.get(roleName));
                                    }
                                }
                            }
                            count = jsonArray.stream().filter(x -> x.getRole().equals("当事人")).count();
                            // 全部替换 才能返回
                            if (count == 0) {
                                return JSON.parseArray(jsonArray.toString(), JSONObject.class);
                            }
                        }
                    }
                }

            }
        }
        return titleList;
    }

    /**
     * QDCT-720 【数据清洗】裁判文书引入同案号缺失主体，走公共诉讼身份解析流程，提升诉讼身份解析精度
     * 1.查询其他维度同案号下所有当事人
     * 2.对比其他维度多的当事人
     * 3.获取当事人在正文中的身份
     * 4.进行当事人补充
     *
     * @return 添加其他维度当事人后的结果
     */
    public static String mergeEntityWithDimensions(String caseNo, String caseRoleEntity, String title, String
            content, String trialStr, String courtCode, boolean sourceTitle, String id, Map<String, String> companyOrPersonKeyNoMapFinal, LocalDateTime dateTime) throws Exception {
        if (StringUtils.isEmpty(caseRoleEntity)) {
            return caseRoleEntity;
            // 接口主体和解析主体去重
        }
        List<CaseRoleExtractEntity_v1> jsonArray = JSONObject.parseArray(caseRoleEntity, CaseRoleExtractEntity_v1.class);

        Map<String, CaseRoleExtractEntity_v1> caseNameMap = jsonArray.stream().collect(Collectors.toMap(CaseRoleExtractEntity_v1::getName, t -> t, (o, n) -> n));
        List<String> caseNameMapKeyno = jsonArray.stream().map(CaseRoleExtractEntity_v1::getKeyNo).collect(Collectors.toList());
        caseNameMapKeyno.addAll(jsonArray.stream().map(CaseRoleExtractEntity_v1::getSupKeyNo).collect(Collectors.toList()));

        List<CaseAllDimNameandkey> list;
        // 使用同案号判断
        if (StringUtils.isNotEmpty(caseNo) && !caseNo.contains("破") && !sourceTitle && StringUtils.isNotEmpty(content)) {
            Condition condition = new Condition(CaseAllDimNameandkey.class);
            String tempAnno = caseNo.replace("（", "(").replace("）", ")");
            condition.createCriteria().andIn("caseno", Arrays.asList(Util.getCompanyNameByName(caseNo), tempAnno))
                    .andNotIn("dataStatus", Arrays.asList(2, 5,93,3,4)).andNotIn("sourcetable", Arrays.asList("risk_fyy_casedetail", "risk_judgement_case"));
            list = CASE_ALL_DIM_NAMEANDKEY_REPOSITORY.selectByCondition(condition);
            // 1.获取其他维度所有当事人信息
            Set<String> names = new HashSet<>();
            Map<String, String> keyNos = new HashedMap();

            list.forEach(x -> {
                if (StringUtils.isNotEmpty(x.getNameandkeyno())) {
                    List<JSONObject> jsonObjectList = JSON.parseArray(x.getNameandkeyno(), JSONObject.class);
                    jsonObjectList.forEach(y -> {

                        String name = y.getString("P");
                        String name2 = y.getString("Name");
                        name = StringUtils.isNotEmpty(name) ? name : name2;

                        String keyNo = y.getString("N");
                        String keyNo2 = y.getString("KeyNo");
                        keyNo = StringUtils.isNotEmpty(keyNo) ? keyNo : keyNo2;
                        if (!caseNameMapKeyno.contains(keyNo) || StringUtils.isEmpty(keyNo)) {
                            names.add(name);
                            keyNos.put(name, keyNo);
                        }

                    });
                }
            });

            //2.对比其他维度比当事人多的当事人
            Set<String> finalNames = names.stream().filter(x -> !caseNameMap.containsKey(x)).collect(Collectors.toSet());
            //3.获取当事人在正文中的身份
            Map<String, String> respRoleMap = new HashedMap();
            if (content.length() > 15) {
                respRoleMap = RoleUtils.getRoleInfo("", content, trialStr, courtCode, caseNo, String.join(",", finalNames), caseNameMap.keySet());
            } else {
                respRoleMap = RoleUtils.getRoleInfo("", "", title + "," + trialStr, courtCode, caseNo, String.join(",", finalNames), caseNameMap.keySet());
            }
            if (respRoleMap.size() == 0) {
                return jsonArray.toString();
            }

            for (String s : respRoleMap.keySet()) {
                String role = respRoleMap.get(s).split("_")[0];
                //正常公司名称进入
                if (!role.equals("当事人") && !validateDataQuality(s)) {
                    Integer roleTag = ConfigStaticDataFile.CASE_ROLE_NAME_MAP_TAG.get(role.replace("(", "（").replace(")", "）"));
                    String companyKeyNo = GetKeyNoOrCerNoHandler.getKeyNoByName(s, dateTime, id, null, caseNo);
                    if (StringUtils.isEmpty(companyKeyNo) && companyOrPersonKeyNoMapFinal.containsKey(s)) {
                        companyKeyNo = companyOrPersonKeyNoMapFinal.get(s);
                    }
                    CaseRoleExtractEntity_v1 caseRoleExtractEntity_v1 = CaseRoleExtractEntity_v1.builder().Role(role).RoleTag(roleTag).KeyNo(companyKeyNo).Name(s).build();
                    caseRoleExtractEntity_v1.setType(58);
                    jsonArray.add(caseRoleExtractEntity_v1);
                }
            }

        }
        // 疑似重复当事人 张三 张三男 李四 李四等
        Set<String> containNames = new HashSet<>();
        for (String key1 : caseNameMap.keySet()) {
            if (key1.length() <= 5 && (key1.endsWith("男") || key1.endsWith("女") || key1.endsWith("等") || key1.endsWith("等人"))) {
                for (String key2 : caseNameMap.keySet()) {
                    if (key2.length() <= 5) {
                        if (key1.contains(key2) && !key1.equals(key2)) {
                            containNames.add(key1);
                            break;
                        }
                    }
                }
            }
        }
        for (String name : containNames) {
            caseNameMap.remove(name);
        }

        return jsonArray.toString();
    }

    /**
     * 非法公司名称拦截规则
     * 具体拦截规则如下
     * 补充的主体只有一个字
     * 补充的主体，超过3个字，包含关键词‘简称|下称|曾用名|现用名’
     * 补充的主体，只出现*号
     * 补充的主体名称=不良资产处置有限公司|黑龙江吉|农村土地承包经营户|农村承包经营户|检察院|本院刑庭|社会保障局|（有限合伙）|公司|（普通合伙）|公司职员|职员|经营户|破产管理人|（特殊普通合伙）|合伙）|管理人|国库|本院|仲裁申请人|异议人|刑庭|农商行
     * 补充的主体，超过5个字，不是以'司|行|队|会|院|局|部|社|厂|厅|所|店|中心|政府|企业|基地|超市|处|矿|室|场|校|城|园|馆|站|组|庭|台|学|吧|庄|户|段|团|村|房|人|家|坊|公寓|库'结尾
     *
     * @param inputData 公司名称
     * @return 是否命中规则
     */
    public static boolean validateDataQuality(String inputData) {
        // 非空验证
        if (inputData == null || inputData.trim().isEmpty()) {
            return true; // 拦截
        }

        // 1. 补充的主体只有一个字
        if (inputData.length() == 1) {
            return true; // 拦截
        }

        // 2. 补充的主体，超过3个字，包含关键词‘简称|下称|曾用名|现用名’
        if (inputData.length() > 3 && inputData.matches(".*(简称|下称|曾用名|现用名|追缴|缴纳|住所地|、).*")) {
            return true; // 拦截
        }

        // 3. 补充的主体，只出现*号
        if (inputData.matches("^[\\*×xX]+$")) {
            return true; // 拦截
        }
        // 3. 补充的主体，只出现*号
        if (inputData.matches("^[：:暨）)].*")) {
            return true; // 拦截
        }
        // 4. 补充的主体名称属于指定名单
        String[] invalidNames = {
                "不良资产处置有限公司", "黑龙江吉", "农村土地承包经营户", "农村承包经营户", "检察院", "本院刑庭",
                "社会保障局", "（有限合伙）", "公司", "（普通合伙）", "公司职员", "职员", "经营户", "破产管理人",
                "（特殊普通合伙）", "合伙）", "管理人", "国库", "本院", "仲裁申请人", "异议人", "刑庭", "农商行"
        };
        for (String name : invalidNames) {
            if (inputData.equals(name)) {
                return true; // 拦截
            }
        }

        // 5. 补充的主体，超过5个字，不是以指定字符结尾
        if (inputData.length() > 4 && !inputData.matches(".*[司|行|队|会|院|局|部|社|厂|厅|所|店|中心|政府|企业|基地|超市|处|矿|室|场|校|城|园|馆|站|组|庭|台|学|吧|庄|户|段|团|村|房|人|家|坊|公寓|库]$")) {
            return true; // 拦截
        }

        // 通过所有验证规则
        return false; // 不拦截
    }
}
