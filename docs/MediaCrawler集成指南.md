# MediaCrawler集成指南

## 1. MediaCrawler项目部署

### 下载和安装MediaCrawler

```bash
# 克隆MediaCrawler项目
git clone https://github.com/NanmiCoder/MediaCrawler.git
cd MediaCrawler

# 安装依赖
pip install -r requirements.txt

# 安装playwright浏览器
playwright install
```

### 配置MediaCrawler

1. **修改配置文件** `config/base_config.py`:

```python
# 平台配置
PLATFORM = "xhs"  # 小红书

# 爬取模式
CRAWLER_TYPE = "search"  # 搜索模式

# 关键词配置
KEYWORDS = ["品牌名称", "产品名称"]

# 爬取数量
CRAWLER_MAX_NOTES_COUNT = 50

# 输出配置
SAVE_DATA_OPTION = "json"  # 保存为JSON格式
```

2. **启动MediaCrawler服务**:

```bash
# 启动爬虫服务
python main.py
```

## 2. 舆情监控系统配置

### 修改application.yml配置

```yaml
sentiment:
  monitor:
    mediacrawler:
      base-url: "http://localhost:8000"  # MediaCrawler服务地址
      timeout: 30000
      retry-count: 3
```

### 创建小红书监控配置

通过API创建监控配置：

```bash
POST http://localhost:8080/api/config/monitor
Content-Type: application/json

{
  "name": "小红书品牌监控",
  "platform": "xiaohongshu",
  "keywords": "[\"品牌名称\", \"产品名称\", \"关键词\"]",
  "intervalSeconds": 300,
  "isEnabled": true,
  "extraConfig": "{\"limit\": 50, \"sort\": \"time\"}"
}
```

## 3. 数据采集流程

### 自动采集流程

1. **定时任务触发**: 系统每分钟检查一次监控配置
2. **调用MediaCrawler**: 根据配置的关键词和平台调用MediaCrawler API
3. **数据处理**: 对返回的数据进行清洗和标准化
4. **情感分析**: 对内容进行情感分析
5. **数据存储**: 将处理后的数据存储到数据库
6. **预警检查**: 检查是否触发预警规则

### 手动采集示例

```bash
# 使用PowerShell调用API
$body = @{
    platform = "xiaohongshu"
    keywords = @("美妆", "护肤")
    limit = 20
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:8080/api/sentiment/collect" -Method POST -Body $body -ContentType "application/json"
```

## 4. MediaCrawler API适配

### 当前实现的接口

系统已经实现了以下MediaCrawler接口适配：

1. **小红书数据获取**: `MediaCrawlerClient.getXiaohongshuData()`
2. **评论数据获取**: `MediaCrawlerClient.getComments()`
3. **健康检查**: `MediaCrawlerClient.checkHealth()`

### 数据格式转换

MediaCrawler返回的数据会被转换为系统标准格式：

```java
// MediaCrawler原始数据
{
  "id": "note_123",
  "title": "笔记标题",
  "desc": "笔记内容",
  "user": {"nickname": "用户名"},
  "interact_info": {
    "liked_count": 100,
    "comment_count": 20
  }
}

// 转换为系统格式
SentimentData {
  platform: "xiaohongshu",
  contentId: "note_123",
  title: "笔记标题",
  content: "笔记内容",
  author: "用户名",
  likeCount: 100,
  commentCount: 20,
  sentiment: "positive",  // 情感分析结果
  sentimentScore: 0.8
}
```

## 5. 常见问题解决

### MediaCrawler连接失败

1. **检查服务状态**:
```bash
GET http://localhost:8080/api/system/health
```

2. **检查MediaCrawler日志**:
```bash
# 查看MediaCrawler运行日志
tail -f logs/crawler.log
```

3. **修复连接问题**:
- 确认MediaCrawler服务正在运行
- 检查端口是否被占用
- 验证网络连接

### 数据采集失败

1. **检查关键词配置**:
```bash
GET http://localhost:8080/api/config/monitor
```

2. **查看系统日志**:
```bash
# 查看应用日志
tail -f logs/sentiment-monitor.log
```

3. **手动测试采集**:
```bash
POST http://localhost:8080/api/sentiment/collect
{
  "platform": "xiaohongshu",
  "keywords": ["测试关键词"],
  "limit": 5
}
```

## 6. 高级配置

### 代理设置

如果需要使用代理，在MediaCrawler中配置：

```python
# config/base_config.py
PROXY_CONFIG = {
    "http": "http://proxy:port",
    "https": "https://proxy:port"
}
```

### 登录配置

小红书可能需要登录才能获取完整数据：

```python
# 配置登录信息
LOGIN_TYPE = "qrcode"  # 二维码登录
COOKIES_FILE = "cookies/xhs_cookies.json"
```

### 反爬虫策略

```python
# 设置请求间隔
REQUEST_DELAY = (2, 5)  # 2-5秒随机延迟

# 设置User-Agent轮换
USER_AGENT_LIST = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    # 更多User-Agent...
]
```

## 7. 监控和维护

### 数据质量监控

```bash
# 查看采集统计
GET http://localhost:8080/api/sentiment/stats/platform

# 查看最近采集的数据
GET http://localhost:8080/api/sentiment/data?platform=xiaohongshu&size=10
```

### 系统健康监控

```bash
# 系统整体状态
GET http://localhost:8080/api/system/health

# 最近活动
GET http://localhost:8080/api/system/activity
```

通过以上配置，您就可以成功采集小红书的数据并进行舆情分析了。
